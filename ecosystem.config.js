module.exports = {
  apps: [
    {
      name: "otoscraper",
      script: "npm",
      args: "run start:prod",
      env: {
        NODE_ENV: "production",
        PORT: "3002"
      },
      watch: false,
      instances: 1,
      autorestart: true,
      max_memory_restart: "1G",
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/pm2-error.log",
      out_file: "logs/pm2-out.log",
      merge_logs: true,
      // Dodatkowe opcje
      exp_backoff_restart_delay: 100, // Opóźnienie restartu przy awariach (ms)
      env_production: {
        NODE_ENV: "production",
        PORT: "3002"
      }
    }
  ]
};
