/**
 * Skrypt do importowania numerów telefonów z pliku TXT, CSV lub JSON
 * i oznaczania ich jako "SMS wysłany"
 * 
 * Użycie:
 * node scripts/import-sms-sent.js --file=plik.txt --format=txt
 * lub
 * node scripts/import-sms-sent.js --file=plik.csv --format=csv
 * lub
 * node scripts/import-sms-sent.js --file=plik.json --format=json
 */
const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

// Inicjalizacja klienta Prisma
const prisma = new PrismaClient();

// Parsowanie argumentów wiersza poleceń
const args = process.argv.slice(2);
let filePath = null;
let fileFormat = null;

for (const arg of args) {
  if (arg.startsWith('--file=')) {
    filePath = arg.split('=')[1];
  } else if (arg.startsWith('--format=')) {
    fileFormat = arg.split('=')[1];
  }
}

// Sprawdzenie argumentów
if (!filePath || !fileFormat) {
  console.error('Błąd: Nie podano ścieżki do pliku lub formatu.');
  console.error('Użycie: node scripts/import-sms-sent.js --file=plik.txt --format=txt');
  console.error('        node scripts/import-sms-sent.js --file=plik.csv --format=csv');
  console.error('        node scripts/import-sms-sent.js --file=plik.json --format=json');
  process.exit(1);
}

// Sprawdzenie, czy format jest obsługiwany
if (fileFormat !== 'txt' && fileFormat !== 'csv' && fileFormat !== 'json') {
  console.error('Błąd: Nieobsługiwany format pliku. Obsługiwane formaty: txt, csv, json');
  process.exit(1);
}

// Sprawdzenie, czy plik istnieje
if (!fs.existsSync(filePath)) {
  console.error(`Błąd: Plik ${filePath} nie istnieje.`);
  process.exit(1);
}

/**
 * Wczytuje numery telefonów z pliku
 * @param {string} filePath - Ścieżka do pliku
 * @param {string} fileFormat - Format pliku (txt, csv lub json)
 * @returns {string[]} - Tablica numerów telefonów
 */
function loadPhoneNumbers(filePath, fileFormat) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    
    if (fileFormat === 'txt') {
      // Dzielenie tekstu na linie i usuwanie pustych linii
      return fileContent.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0 && !isNaN(Number(line.replace(/\D/g, ''))));
    } else if (fileFormat === 'csv') {
      // Dzielenie tekstu na linie i przetwarzanie każdej linii CSV
      return fileContent.split('\n')
        .map(line => {
          // Obsługa pól w cudzysłowach (np. "48123456789")
          const match = line.match(/"([^"]+)"/) || line.match(/([^,]+)/);
          return match ? match[1].trim() : '';
        })
        .filter(line => line.length > 0 && !isNaN(Number(line.replace(/\D/g, ''))));
    } else if (fileFormat === 'json') {
      const jsonData = JSON.parse(fileContent);
      
      // Obsługa różnych formatów JSON
      if (Array.isArray(jsonData)) {
        // Jeśli JSON to tablica, spróbuj pobrać numery telefonów
        if (typeof jsonData[0] === 'string') {
          // Format: ["123456789", "987654321"]
          return jsonData;
        } else if (typeof jsonData[0] === 'object') {
          // Format: [{"number": "123456789"}, {"number": "987654321"}]
          // lub [{"phone": "123456789"}, {"phone": "987654321"}]
          return jsonData.map(item => item.number || item.phone || '').filter(Boolean);
        }
      } else if (typeof jsonData === 'object') {
        // Format: {"numbers": ["123456789", "987654321"]}
        // lub {"phones": ["123456789", "987654321"]}
        if (Array.isArray(jsonData.numbers)) {
          return jsonData.numbers;
        } else if (Array.isArray(jsonData.phones)) {
          return jsonData.phones;
        }
      }
      
      console.error('Błąd: Nieprawidłowy format JSON. Oczekiwano tablicy numerów lub tablicy obiektów z polem "number" lub "phone".');
      process.exit(1);
    }
  } catch (error) {
    console.error(`Błąd podczas wczytywania pliku: ${error.message}`);
    process.exit(1);
  }
  
  return [];
}

/**
 * Normalizuje numer telefonu (usuwa spacje, myślniki, nawiasy, prefiks +48/48)
 * @param {string} phone - Numer telefonu do normalizacji
 * @returns {string} - Znormalizowany numer telefonu (zawsze 9-cyfrowy)
 */
function normalizePhoneNumber(phone) {
  if (!phone) return '';
  
  // Usuń wszystkie nie-cyfry
  let cleaned = phone.replace(/\D/g, '');
  
  // Usuń prefiks +48 lub 48 (jeśli jest)
  if (cleaned.startsWith('48')) {
    cleaned = cleaned.substring(2);
  }
  
  // Sprawdź czy mamy 9-cyfrowy numer
  if (cleaned.length !== 9) {
    console.warn(`Ostrzeżenie: Numer ${phone} po normalizacji ma ${cleaned.length} cyfr zamiast 9.`);
  }
  
  return cleaned;
}

/**
 * Główna funkcja skryptu
 */
async function processPhoneNumbers() {
  try {
    console.log(`Wczytywanie numerów telefonów z pliku ${filePath} (format: ${fileFormat})...`);
    
    // Wczytaj numery telefonów z pliku
    const phoneNumbers = loadPhoneNumbers(filePath, fileFormat);
    
    if (phoneNumbers.length === 0) {
      console.error('Błąd: Nie znaleziono numerów telefonów w pliku.');
      process.exit(1);
    }
    
    // Normalizacja numerów telefonów (usuń spacje, myślniki, itd.)
    const normalizedPhoneNumbers = phoneNumbers.map(normalizePhoneNumber).filter(Boolean);
    
    console.log(`Znaleziono ${normalizedPhoneNumbers.length} numerów telefonów.`);
    
    // Znajdź ogłoszenia powiązane z tymi numerami
    console.log('Wyszukiwanie ogłoszeń powiązanych z numerami telefonów...');
    
    const phones = await prisma.phone.findMany({
      where: {
        number: {
          in: normalizedPhoneNumbers
        }
      },
      include: {
        ad: true
      }
    });
    
    const adIds = [...new Set(phones.map(phone => phone.adId))];
    
    console.log(`Znaleziono ${phones.length} numerów telefonów w bazie i ${adIds.length} powiązanych ogłoszeń.`);
    
    // Aktualizuj ogłoszenia - oznacz jako "SMS wysłany"
    if (adIds.length > 0) {
      console.log('Aktualizowanie statusu ogłoszeń...');
      
      const now = new Date();
      
      await prisma.ad.updateMany({
        where: {
          id: {
            in: adIds
          }
        },
        data: {
          smsSent: true,
          smsSentAt: now
        }
      });
      
      console.log(`Zaktualizowano ${adIds.length} ogłoszeń (oznaczono jako "SMS wysłany").`);
    }
    
    // Dodaj wpisy do historii wysłanych SMS-ów (aby były w karencji)
    console.log('Dodawanie wpisów do historii wysłanych SMS-ów...');
    
    const now = new Date();
    const smsHistoryEntries = normalizedPhoneNumbers.map(number => ({
      number,
      sentAt: now
    }));
    
    // Sprawdź, czy istnieją już wpisy w historii dla tych numerów
    const existingHistory = await prisma.sentSmsHistory.findMany({
      where: {
        number: {
          in: normalizedPhoneNumbers
        }
      },
      select: {
        number: true
      }
    });
    
    const existingNumbers = new Set(existingHistory.map(h => h.number));
    const newEntries = smsHistoryEntries.filter(entry => !existingNumbers.has(entry.number));
    
    if (newEntries.length > 0) {
      await prisma.sentSmsHistory.createMany({
        data: newEntries
      });
    }
    
    console.log(`Dodano ${newEntries.length} nowych wpisów do historii wysłanych SMS-ów.`);
    
    // Usuń numery z listy do wysłania
    console.log('Usuwanie numerów z listy do wysłania...');
    
    const deletedPhones = await prisma.phoneToSend.deleteMany({
      where: {
        number: {
          in: normalizedPhoneNumbers
        }
      }
    });
    
    console.log(`Usunięto ${deletedPhones.count} numerów z listy do wysłania.`);
    
    // Podsumowanie
    console.log('\nPodsumowanie:');
    console.log(`- Zaimportowano ${normalizedPhoneNumbers.length} numerów telefonów`);
    console.log(`- Znaleziono ${phones.length} numerów w bazie danych`);
    console.log(`- Zaktualizowano ${adIds.length} ogłoszeń (oznaczono jako "SMS wysłany")`);
    console.log(`- Dodano ${newEntries.length} nowych wpisów do historii wysłanych SMS-ów`);
    console.log(`- Usunięto ${deletedPhones.count} numerów z listy do wysłania`);
    
    console.log('\nOperacja zakończona pomyślnie.');
  } catch (error) {
    console.error(`Błąd podczas przetwarzania: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Uruchom główną funkcję
processPhoneNumbers();
