const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log("Usuwanie zduplikowanych selektorów...");
  
  // Pobierz wszystkie selektory posortowane według nazwy i ID (rosnąco)
  const selectors = await prisma.selector.findMany({
    orderBy: [
      { name: 'asc' },
      { id: 'asc' }
    ]
  });
  
  // Zidentyfikuj unikalne nazwy selektorów i ich pierwsze wystąpienia (z najniższym ID)
  const uniqueNames = new Set();
  const selectorsToKeep = [];
  const selectorsToDelete = [];
  
  selectors.forEach(selector => {
    if (!uniqueNames.has(selector.name)) {
      // Zachowaj pierwszy napotkany selektor o danej nazwie
      uniqueNames.add(selector.name);
      selectorsToKeep.push(selector);
    } else {
      // Oznacz pozostałe jako do usunięcia
      selectorsToDelete.push(selector);
    }
  });
  
  console.log(`Znaleziono ${selectorsToDelete.length} zduplikowanych selektorów do usunięcia.`);
  
  if (selectorsToDelete.length > 0) {
    // Usuń zduplikowane selektory
    console.log("Selektory do usunięcia:");
    for (const selector of selectorsToDelete) {
      console.log(`- ID: ${selector.id}, Nazwa: ${selector.name}`);
      await prisma.selector.delete({
        where: { id: selector.id }
      });
    }
    
    console.log("\nSelektory pozostawione w bazie danych:");
    for (const selector of selectorsToKeep) {
      console.log(`- ID: ${selector.id}, Nazwa: ${selector.name}, Wartość: ${selector.selectorValue}`);
    }
    
    console.log("\nOperacja zakończona pomyślnie. Zduplikowane selektory zostały usunięte.");
  } else {
    console.log("Brak zduplikowanych selektorów do usunięcia.");
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error("Wystąpił błąd:", e);
    await prisma.$disconnect();
    process.exit(1);
  });
