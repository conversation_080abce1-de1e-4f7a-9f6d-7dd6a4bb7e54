const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  const selectors = await prisma.selector.findMany({
    orderBy: { name: 'asc' }
  });
  
  console.log("Liczba selektorów w bazie danych:", selectors.length);
  console.log("Lista selektorów:");
  
  // Sprawdzenie, czy istnieją zduplikowane nazwy selektorów
  const nameCount = {};
  selectors.forEach(s => {
    if (!nameCount[s.name]) {
      nameCount[s.name] = 1;
    } else {
      nameCount[s.name]++;
    }
  });
  
  // Wyświetl listę selektorów
  selectors.forEach(s => {
    console.log(`- ID: ${s.id}, Nazwa: ${s.name}, Typ: ${s.selectorType}, Wartość: ${s.selectorValue}`);
  });
  
  // Wyświetl zduplikowane nazwy
  console.log("\nZduplikowane nazwy selektorów:");
  let hasDuplicates = false;
  for (const [name, count] of Object.entries(nameCount)) {
    if (count > 1) {
      hasDuplicates = true;
      console.log(`- ${name}: ${count} wystąpienia`);
      // Wyświetl szczegóły zduplikowanych selektorów
      const duplicates = selectors.filter(s => s.name === name);
      duplicates.forEach(d => {
        console.log(`  * ID: ${d.id}, Typ: ${d.selectorType}, Wartość: ${d.selectorValue}`);
      });
    }
  }
  
  if (!hasDuplicates) {
    console.log("Brak zduplikowanych nazw.");
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
