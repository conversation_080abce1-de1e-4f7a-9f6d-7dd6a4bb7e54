const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  const email = '<EMAIL>';
  const password = 'aleurwal69!';
  
  // Sprawdź, czy użytkownik już istnieje
  const existingUser = await prisma.user.findUnique({
    where: { email },
  });
  
  if (existingUser) {
    console.log(`Użytkownik ${email} już istnieje.`);
    return;
  }
  
  // Zahas<PERSON>j hasło
  const saltRounds = 10;
  const hashedPassword = await bcrypt.hash(password, saltRounds);
  
  // Utwórz użytkownika
  const user = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
    },
  });
  
  console.log(`Utworzono użytkownika: ${user.email}`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
