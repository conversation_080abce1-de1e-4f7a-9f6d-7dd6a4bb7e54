// Skrypt do usunięcia ogłoszeń pominiętych z powodu niedozwolonego województwa
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function removeIgnoredRegionAds() {
  console.log('Rozpoczynam usuwanie ogłoszeń pominiętych z powodu niedozwolonego województwa...');
  
  try {
    // Najpierw wyświetl ile ogłoszeń zostanie usuniętych
    const adsToDelete = await prisma.ad.findMany({
      where: {
        brandModel: 'Auto (pominięto - złe województwo)'
      },
      select: {
        id: true,
        externalId: true,
        brandModel: true,
        location: true,
      }
    });
    
    console.log(`Znaleziono ${adsToDelete.length} ogłoszeń do usunięcia`);
    
    if (adsToDelete.length > 0) {
      console.log('Pierwsze 5 ogłos<PERSON><PERSON> do usunięcia:');
      console.table(adsToDelete.slice(0, 5));
      
      // Wykonaj usunięcie
      const deleteResult = await prisma.ad.deleteMany({
        where: {
          brandModel: 'Auto (pominięto - złe województwo)'
        }
      });
      
      console.log(`Usunięto ${deleteResult.count} ogłoszeń.`);
      console.log('Operacja zakończona pomyślnie.');
    } else {
      console.log('Brak ogłoszeń do usunięcia.');
    }
  } catch (error) {
    console.error('Wystąpił błąd podczas usuwania ogłoszeń:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Uruchom funkcję
removeIgnoredRegionAds();
