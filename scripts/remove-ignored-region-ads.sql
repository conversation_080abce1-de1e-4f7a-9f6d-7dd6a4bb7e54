-- SQL skrypt do usunięcia ogłoszeń pominiętych z powodu niedozwolonego województwa
-- Uwaga: Relacja między tabelami Ad i Phone ma klauzulę onDelete: Cascade, 
-- więc powiązane numery telefonów zostaną usunięte automatycznie

-- Usunięcie ogłoszeń z tytułem "Auto (pominięto - złe województwo)"
DELETE FROM "Ad" 
WHERE "brandModel" = 'Auto (pominięto - złe województwo)';

-- Alternatywnie, można użyć zapytania LIKE, jeśli format tytułów może się nieco różnić
-- DELETE FROM "Ad" 
-- WHERE "brandModel" LIKE 'Auto (pominięto - złe województwo)%';

-- Aby tyl<PERSON> w<PERSON>ć informacje o ogłoszeniach przed usunięciem, użyj:
-- SELECT id, externalId, brandModel, location FROM "Ad" 
-- WHERE "brandModel" = 'Auto (pominięto - złe województwo)';
