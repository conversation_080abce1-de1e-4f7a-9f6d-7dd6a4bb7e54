#!/bin/bash

# Upewnij się, że katalog na certyfikaty istnieje
mkdir -p certificates

# Utwórz własny CA (Certificate Authority)
openssl genrsa -out certificates/rootCA.key 2048
openssl req -x509 -new -nodes -key certificates/rootCA.key -sha256 -days 365 -out certificates/rootCA.pem -subj "/C=PL/ST=Mazowieckie/L=Warszawa/O=OtoScraper/CN=OtoScraperCA"

# Utwórz certyfikat dla scraper.local
openssl genrsa -out certificates/scraper.local.key 2048

# Utwórz plik konfiguracyjny dla certyfikatu
cat > certificates/scraper.local.conf << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = req_ext

[dn]
C=PL
ST=Mazowieckie
L=Warszawa
O=OtoScraper
OU=Development
CN=scraper.local

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = scraper.local
DNS.2 = *.scraper.local
EOF

# Wygeneruj żądanie podpisania certyfikatu (CSR)
openssl req -new -key certificates/scraper.local.key -out certificates/scraper.local.csr -config certificates/scraper.local.conf

# Podpisz CSR za pomocą naszego CA
openssl x509 -req -in certificates/scraper.local.csr -CA certificates/rootCA.pem -CAkey certificates/rootCA.key -CAcreateserial -out certificates/scraper.local.crt -days 365 -extensions req_ext -extfile certificates/scraper.local.conf

echo "-----------------------------------------------------"
echo "✅ Certyfikaty zostały wygenerowane w katalogu 'certificates'!"
echo "-----------------------------------------------------"
echo "Aby dodać domenę do pliku hosts, wykonaj:"
echo "sudo echo '127.0.0.1 scraper.local' >> /etc/hosts"
echo "-----------------------------------------------------"
echo "Aby uruchomić serwer HTTPS z certyfikatem, użyj:"
echo "npm run dev:https:cert"
echo "-----------------------------------------------------"
echo "Aby przeglądarka ufała certyfikatowi, dodaj certificates/rootCA.pem do zaufanych certyfikatów w systemie"
echo "-----------------------------------------------------"
