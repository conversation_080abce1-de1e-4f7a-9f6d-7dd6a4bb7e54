# OtoScraper

Aplikacja do scrapowania ogłoszeń z OtoMoto.pl i wysyłania SMS-ów do sprzedających.

## Funkcjonalności

- Scrapowanie ogłoszeń z OtoMoto.pl
- Przechowywanie danych o ogłoszeniach w bazie danych
- Zarządzanie listą numerów telefonów do wysyłki SMS-ów
- Zarządzanie listą zablokowanych numerów telefonów
- Konfiguracja selektorów CSS do scrapowania
- Konfiguracja ustawień scrapera
- Konfiguracja ustawień aplikacji
- Statystyki wysłanych SMS-ów i zescrapowanych ogłoszeń
- Integracja z bramką SMS T-Mobile

## Technologie

- Next.js 14 (React + Node.js)
- TypeScript
- Prisma (ORM)
- SQLite (baza danych)
- Tailwind CSS (stylowanie)
- Puppeteer (scrapowanie)

## Wymagania

- Node.js 18+
- npm lub yarn

## Instalacja

1. Sklonuj repozytorium:

```bash
git clone https://github.com/twoj-username/otoscraper.git
cd otoscraper
```

2. Zainstaluj zależności:

```bash
npm install
# lub
yarn install
```

3. Skonfiguruj bazę danych:

```bash
npx prisma generate
npx prisma db push
```

4. Skonfiguruj zmienne środowiskowe:

Skopiuj plik `.env.example` do `.env` i dostosuj ustawienia:

```bash
cp .env.example .env
```

5. Uruchom aplikację:

```bash
npm run dev
# lub
yarn dev
```

Aplikacja będzie dostępna pod adresem: http://localhost:3000

## Konfiguracja

### Ustawienia scrapera

W panelu administracyjnym aplikacji możesz skonfigurować:

- URL do scrapowania
- Częstotliwość scrapowania
- Selektory CSS do pobierania danych
- Ustawienia Puppeteer (headless, slowMo, itp.)

### Integracja z bramką SMS T-Mobile

Aby skonfigurować integrację z bramką SMS T-Mobile, należy:

1. Umieścić certyfikaty w katalogu `src/smsapi/certificates/`
2. Skonfigurować dane dostępowe w ustawieniach aplikacji

## Struktura projektu

```
otoscraper/
├── prisma/                # Konfiguracja Prisma ORM
├── public/                # Statyczne pliki
├── src/
│   ├── app/               # Strony aplikacji (Next.js App Router)
│   ├── components/        # Komponenty React
│   ├── lib/               # Biblioteki i narzędzia
│   │   ├── scrapers/      # Scrapery (Puppeteer)
│   │   └── utils/         # Funkcje pomocnicze
│   └── smsapi/            # Integracja z bramką SMS
└── ...
```

## Licencja

Ten projekt jest licencjonowany na warunkach licencji MIT.
