const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

// Ustawienia środowiska - dev lub produkcja
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Ścieżki do certyfikatów
const certPath = path.join(__dirname, 'certificates', 'scraper.local.crt');
const keyPath = path.join(__dirname, 'certificates', 'scraper.local.key');

// Sprawdź czy certyfikaty istnieją
let httpsOptions = {
  // Domyślnie ignorujemy problemy z certyfikatem
  rejectUnauthorized: false
};

// Próba załadowania certyfikatów jeśli istnieją
try {
  if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
    console.log('> Znaleziono certyfikaty, używam ich do uruchomienia serwera HTTPS');
    httpsOptions = {
      key: fs.readFileSync(keyPath),
      cert: fs.readFileSync(certPath)
    };
  } else {
    console.log('> Nie znaleziono certyfikatów w katalogu certificates/');
    console.log('> Uruchom skrypt scripts/generate-cert.sh aby wygenerować certyfikaty');
    console.log('> Używam domyślnych opcji HTTPS bez certyfikatu');
  }
} catch (error) {
  console.error('> Błąd podczas ładowania certyfikatów:', error);
  console.log('> Używam domyślnych opcji HTTPS bez certyfikatu');
}

// Port serwera
const PORT = parseInt(process.env.PORT || '3443', 10);

app.prepare().then(() => {
  createServer(httpsOptions, (req, res) => {
    // Parsuj URL
    const parsedUrl = parse(req.url, true);
    
    // Przekazanie zapytań do Next.js
    handle(req, res, parsedUrl);
  }).listen(PORT, (err) => {
    if (err) throw err;
    console.log(`> Serwer HTTPS działa na https://localhost:${PORT}`);
    
    if (httpsOptions.key && httpsOptions.cert) {
      console.log('> Używam certyfikatów z katalogu certificates/');
      console.log('> Możesz użyć także domeny scraper.local jeśli dodałeś ją do pliku hosts:');
      console.log('> https://scraper.local:3443');
    } else {
      console.log('> NODE_TLS_REJECT_UNAUTHORIZED=0 - ignorowanie problemów z certyfikatem SSL');
      console.log('> W przeglądarce możesz zobaczyć ostrzeżenie o braku zaufanego certyfikatu');
    }
  });
});
