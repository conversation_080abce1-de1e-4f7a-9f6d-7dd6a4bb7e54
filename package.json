{"name": "o<PERSON><PERSON><PERSON>er", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "NODE_TLS_REJECT_UNAUTHORIZED=0 HTTPS=true next dev", "dev:https:custom": "NODE_TLS_REJECT_UNAUTHORIZED=0 HTTPS=true node server.js", "dev:http-server": "node https-server.js", "dev:https:cert": "NODE_TLS_REJECT_UNAUTHORIZED=0 node https-cert-server.js", "generate-cert": "cd scripts && chmod +x generate-cert.sh && ./generate-cert.sh", "build": "next build", "start": "next start", "start:prod": "NODE_ENV=production node server.js", "cron": "node server.js", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@prisma/client": "^5.10.0", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/themes": "^3.2.1", "axios": "^1.8.3", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "fast-xml-parser": "^4.3.4", "google-libphonenumber": "^3.2.40", "https": "^1.0.0", "jose": "^6.0.8", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.479.0", "next": "15.2.1", "next-themes": "^0.2.1", "node-cron": "^3.0.3", "puppeteer": "^22.0.0", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/google-libphonenumber": "^7.4.30", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.2.1", "postcss": "^8", "prisma": "^5.10.0", "typescript": "^5"}}