/** @type {import('next').NextConfig} */
const { createServer } = require('https');
const { readFileSync } = require('fs');
const { parse } = require('url');
const { join } = require('path');
const next = require('next');

// Konfiguracja Next.js
const nextConfig = {
  reactStrictMode: true,
  // images.domains - pozwala na optymalizację obrazów z tej domeny poprzez komponent next/image
  images: {
    domains: ['ireland.apollo.olxcdn.com'],
  },
  // Przeniesione z experimental.serverComponentsExternalPackages
  serverExternalPackages: ['puppeteer'],
};

// Jeśli uruchamiamy z HTTPS=true, tworzymy niestandardowy serwer HTTPS
if (process.env.HTTPS === 'true') {
  const app = next({ dev: process.env.NODE_ENV !== 'production', conf: nextConfig });
  const handle = app.getRequestHandler();

  app.prepare().then(() => {
    // Opcje serwera HTTPS - ignorujemy problemy z certyfikatem
    const httpsOptions = {
      // W trybie produkcyjnym należy dostarczyć własne certyfikaty
      // Dla trybu deweloperskiego używamy self-signed
      rejectUnauthorized: false
    };

    createServer(httpsOptions, (req, res) => {
      const parsedUrl = parse(req.url, true);
      handle(req, res, parsedUrl);
    }).listen(3443, (err) => {
      if (err) throw err;
      console.log('> Serwer HTTPS działa na https://localhost:3443');
    });
  });
} else {
  // Standardowa eksportacja konfiguracji dla HTTP
  module.exports = nextConfig;
}
