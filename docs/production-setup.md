# Wdrożenie produkcyjne OtoScraper

Ten dokument zawiera instrukcje dotyczące wdrożenia aplikacji OtoScraper w środowisku produkcyjnym z uruchomionym cronem do automatycznego scrapowania.

## Wymagania wstępne

- Node.js (wersja 18 lub nowsza)
- NPM (wersja 7 lub nowsza)
- Baza danych SQLite (już zintegrowana z projektem)
- Dostęp do serwera z możliwością uruchamiania procesów w tle
- (Opcjonalnie) PM2 do zarządzania procesami

## Kroki wdrożenia

### 1. Przygotowanie środowiska

1. Sklonuj repozytorium projektu:
   ```bash
   git clone [adres-repozytorium] otoscraper
   cd otoscraper
   ```

2. Zainstaluj zależ<PERSON>ści:
   ```bash
   npm install
   ```

3. Skopiuj plik .env.example i skonfiguruj go:
   ```bash
   cp .env.example .env
   ```

4. Zmodyfikuj plik .env wpisując odpowiednie dane:
   ```
   DATABASE_URL="file:./dev.db"
   JWT_SECRET="twój-sekretny-klucz"
   # Dodaj inne wymagane zmienne środowiskowe
   ```

### 2. Budowanie aplikacji

Zbuduj aplikację w wersji produkcyjnej:

```bash
npm run build
```

### 3. Uruchomienie aplikacji z cronem

Aplikacja OtoScraper może być uruchomiona w trybie produkcyjnym z wbudowanym cronowym serwerem, który automatycznie wykonuje zadania scrapowania zgodnie z ustawieniami z bazy danych.

```bash
npm run start:prod
```

Ten skrypt uruchamia `server.js` z ustawioną zmienną NODE_ENV=production, co powoduje:
- Inicjalizację aplikacji Next.js w trybie produkcyjnym
- Uruchomienie serwera HTTP
- Automatyczną inicjalizację crona na podstawie ustawień w bazie danych
- Obsługę zadań cyklicznych (scrapowanie, czyszczenie historii SMS-ów)

### 4. Zarządzanie procesem za pomocą PM2 (rekomendowane)

Dla stabilnego działania w środowisku produkcyjnym, zalecamy używanie PM2 do zarządzania procesem:

1. Zainstaluj PM2 globalnie:
   ```bash
   npm install -g pm2
   ```

2. Uruchom aplikację przez PM2:
   ```bash
   pm2 start npm --name "otoscraper" -- run start:prod
   ```

3. Skonfiguruj automatyczny restart po ponownym uruchomieniu serwera:
   ```bash
   pm2 startup
   pm2 save
   ```

4. Monitorowanie i zarządzanie:
   ```bash
   pm2 status        # Sprawdź status
   pm2 logs otoscraper  # Pokaż logi
   pm2 restart otoscraper  # Zrestartuj aplikację
   pm2 stop otoscraper     # Zatrzymaj aplikację
   ```

### 5. Konfiguracja crona przez panel administracyjny

Po uruchomieniu aplikacji, zaloguj się do panelu administracyjnego i przejdź do sekcji Ustawienia, aby skonfigurować:

1. Częstotliwość scrapowania (w minutach)
2. Włączenie lub wyłączenie automatycznego crona
3. URL do scrapowania
4. Inne ustawienia związane ze scrapowaniem

### 6. Rozwiązywanie problemów

- Sprawdź logi aplikacji w katalogu `/logs/scraper.log`
- Jeśli używasz PM2, sprawdź logi poleceniem `pm2 logs otoscraper`
- Upewnij się, że port 3002 (lub inny skonfigurowany) jest dostępny i nie jest blokowany przez firewall

## Zasoby dodatkowe

- [Dokumentacja PM2](https://pm2.keymetrics.io/docs/usage/quick-start/)
- [Dokumentacja Next.js dla wdrożenia produkcyjnego](https://nextjs.org/docs/deployment)
