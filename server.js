const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');
const cron = require('node-cron');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Ustawienia środowiska - dev lub produkcja
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Usunięto opcje HTTPS, używamy HTTP

// Port serwera
const PORT = parseInt(process.env.PORT || '3002', 10);

// Zmienne przechowujące zadania cron
let cronJob = null;
let cleanupCronJob = null;
let leaseRemindersCronJob = null;
let currentScrapingPage = 0;
let isScrapingActive = false;
let scraperBrowser = null; // Referencja do przeglądarki puppeteer
let currentScrapedAdIds = []; // Lista aktualnie zescrapowanych ID ogłoszeń

// Funkcja do usuwania niedokończonych scrapowań (z tylko 1 stroną)
async function cleanupIncompleteScrapings() {
  try {
    console.log('Rozpoczynanie czyszczenia niedokończonych scrapowań...');
    
    // Znajdź scrapowania z tylko 1 stroną
    const incompleteScrapings = await prisma.scrapingSession.findMany({
      where: {
        currentPage: 1
      }
    });
    
    if (incompleteScrapings.length > 0) {
      console.log(`Znaleziono ${incompleteScrapings.length} niedokończonych scrapowań do usunięcia`);
      
      // Usuń znalezione scrapowania
      const deleteResult = await prisma.scrapingSession.deleteMany({
        where: {
          currentPage: 1
        }
      });
      
      console.log(`Usunięto ${deleteResult.count} niedokończonych scrapowań`);
    } else {
      console.log('Nie znaleziono niedokończonych scrapowań do usunięcia');
    }
    
    return incompleteScrapings.length;
  } catch (error) {
    console.error('Błąd podczas czyszczenia niedokończonych scrapowań:', error);
    return 0;
  }
}

// Funkcja do wysyłania emaila z przypomnieniami o leasingu na dzisiaj
async function sendLeaseRemindersEmail() {
  try {
    console.log('Sprawdzanie przypomnień o leasingu na dzisiaj...');
    
    // Wywołaj API do wysłania emaila z przypomnieniami
    const response = await fetch(`http://localhost:${PORT}/api/cron/lease-reminders`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`Email z przypomnieniami o leasingu został wysłany. Liczba przypomnień: ${result.count}`);
    } else {
      console.log('Brak przypomnień o leasingu na dzisiaj lub wystąpił błąd podczas wysyłania emaila');
    }
    
    return result;
  } catch (error) {
    console.error('Błąd podczas wysyłania emaila z przypomnieniami o leasingu:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Funkcja do czyszczenia starych wpisów z SentSmsHistory
async function cleanupSentSmsHistory() {
  try {
    console.log('Rozpoczynanie czyszczenia historii wysłanych SMS-ów...');
    
    // Pobierz ustawienia z bazy danych
    const settings = await prisma.settings.findFirst();
    
    if (!settings) {
      console.error('Nie znaleziono ustawień w bazie danych');
      return;
    }
    
    // Pobierz okres karencji z ustawień
    const smsResendDays = settings.smsResendDays || 90; // Domyślnie 90 dni
    
    // Oblicz datę graniczną
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - smsResendDays);
    
    // Usuń stare wpisy
    const result = await prisma.sentSmsHistory.deleteMany({
      where: {
        sentAt: {
          lt: cutoffDate
        }
      }
    });
    
    console.log(`Usunięto ${result.count} starych wpisów z historii wysłanych SMS-ów`);
    
    return result.count;
  } catch (error) {
    console.error('Błąd podczas czyszczenia historii wysłanych SMS-ów:', error);
    return 0;
  }
}

// Funkcja do zatrzymywania scrapera
async function stopScraper() {
  try {
    console.log('Zatrzymywanie scrapera...');
    
    // Jeśli mamy referencję do przeglądarki, zamknij ją
    if (scraperBrowser) {
      await scraperBrowser.close().catch(e => console.error('Błąd podczas zamykania przeglądarki:', e));
      scraperBrowser = null;
    }
    
    // Zatrzymaj również procesy Cheerio
    try {
      // Zaimportuj kontroler Cheerio
      const { CheerioController } = require('./src/lib/scrapers/cheerio-helper');
      // Zatrzymaj wszystkie bieżące procesy Cheerio
      const controller = CheerioController.getInstance();
      controller.stopProcessing();
      console.log('Zatrzymano również procesy Cheerio');
      
      // Znajdź aktywną sesję scrapowania i ustaw jej status na paused
      try {
        const activeSessions = await prisma.scrapingSession.findMany({
          where: {
            OR: [
              { status: 'in_progress' },
              { status: 'collecting_ids' },
              { status: 'collecting_details' }
            ]
          }
        });
        
        if (activeSessions.length > 0) {
          for (const session of activeSessions) {
            await prisma.scrapingSession.update({
              where: { id: session.id },
              data: {
                status: 'paused',
                pausedAt: new Date(),
                pauseDuration: 600, // 10 minut
                pauseReason: 'manual_stop'
              }
            });
            console.log(`Zaktualizowano status sesji ${session.id} na paused z powodu ręcznego zatrzymania`);
          }
        }
      } catch (sessionError) {
        console.error('Błąd podczas aktualizacji statusu sesji:', sessionError);
      }
    } catch (cheerioError) {
      console.error('Błąd podczas zatrzymywania procesów Cheerio:', cheerioError);
    }
    
    // Zresetuj stan scrapera
    isScrapingActive = false;
    currentScrapingPage = 0;
    
    console.log('Scraper zatrzymany pomyślnie');
    return true;
  } catch (error) {
    console.error('Błąd podczas zatrzymywania scrapera:', error);
    return false;
  }
}

// Funkcja do uruchamiania scrapera
async function runScraper() {
  try {
    if (isScrapingActive) {
      console.log('Scraping już jest aktywny, pomijam uruchomienie');
      return;
    }

    isScrapingActive = true;
    currentScrapingPage = 0;
    
    console.log('Uruchamianie scrapera...');
    
    // Zapisz informację o rozpoczęciu scrapowania
    await prisma.settings.updateMany({
      data: {
        lastScrapedAt: new Date()
      }
    });
    
    // Wywołaj API scrapera
    const response = await fetch(`http://localhost:${PORT}/api/scraper`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    console.log('Wynik scrapowania:', result);
    
    isScrapingActive = false;
  } catch (error) {
    console.error('Błąd podczas uruchamiania scrapera:', error);
    isScrapingActive = false;
  }
}

// Funkcja do inicjalizacji crona
async function initCron() {
  try {
    // Pobierz ustawienia z bazy danych
    const settings = await prisma.settings.findFirst();
    
    if (!settings) {
      console.error('Nie znaleziono ustawień w bazie danych');
      return;
    }
    
    // Jeśli cron jest włączony, uruchom go
    if (settings.cronEnabled) {
      const interval = settings.scrapingInterval || 60; // Domyślnie 60 minut
      
      // Zatrzymaj istniejący cron, jeśli istnieje
      if (cronJob) {
        cronJob.stop();
      }
      
      // Utwórz nowy cron z odpowiednim interwałem
      // Format: minute hour day month day_of_week
      // */interval * * * * - co 'interval' minut
      cronJob = cron.schedule(`*/${interval} * * * *`, runScraper);
      
      console.log(`Cron uruchomiony z interwałem ${interval} minut`);
    } else {
      // Zatrzymaj cron, jeśli jest wyłączony w ustawieniach
      if (cronJob) {
        cronJob.stop();
        cronJob = null;
        console.log('Cron zatrzymany');
      }
    }
    
    // Zawsze uruchamiaj cron do czyszczenia historii SMS-ów (raz dziennie o północy)
    if (cleanupCronJob) {
      cleanupCronJob.stop();
    }
    
    cleanupCronJob = cron.schedule('0 0 * * *', cleanupSentSmsHistory);
    console.log('Cron czyszczenia historii SMS-ów uruchomiony (codziennie o północy)');
    
    // Uruchom czyszczenie od razu przy starcie serwera
    cleanupSentSmsHistory();
    
    // Uruchom cron do wysyłania emaila z przypomnieniami o leasingu (codziennie o 8:00 rano)
    if (leaseRemindersCronJob) {
      leaseRemindersCronJob.stop();
    }
    
    leaseRemindersCronJob = cron.schedule('0 8 * * *', sendLeaseRemindersEmail);
    console.log('Cron wysyłania emaila z przypomnieniami o leasingu uruchomiony (codziennie o 8:00 rano)');
  } catch (error) {
    console.error('Błąd podczas inicjalizacji crona:', error);
  }
}

// Endpoint do sprawdzania statusu crona
async function getCronStatus() {
  try {
    const settings = await prisma.settings.findFirst();
    
    return {
      enabled: settings?.cronEnabled || false,
      interval: settings?.scrapingInterval || 60,
      lastScrapedAt: settings?.lastScrapedAt || null,
      isActive: (cronJob !== null && settings?.cronEnabled) || isScrapingActive,
      currentScrapingPage: isScrapingActive ? currentScrapingPage : null,
      isScrapingActive,
      cleanupCronActive: cleanupCronJob !== null,
      leaseRemindersCronActive: leaseRemindersCronJob !== null
    };
  } catch (error) {
    console.error('Błąd podczas pobierania statusu crona:', error);
    return {
      enabled: false,
      interval: 60,
      lastScrapedAt: null,
      isActive: false,
      currentScrapingPage: null,
      isScrapingActive: false,
      cleanupCronActive: false,
      leaseRemindersCronActive: false,
      error: error.message
    };
  }
}

// Funkcja do ręcznego uruchomienia wysyłania emaila z przypomnieniami o leasingu
async function runSendLeaseRemindersEmail() {
  return await sendLeaseRemindersEmail();
}

// Endpoint do aktualizacji statusu scrapowania
function updateScrapingStatus(page) {
  currentScrapingPage = page;
  // Automatycznie aktualizuj stan aktywności scrapera na podstawie numeru strony
  if (page > 0) {
    isScrapingActive = true;
  } else if (page === 0) {
    isScrapingActive = false;
  }
  // Nie zmieniamy stanu gdy page jest null lub undefined
}

// Funkcja do ręcznego uruchomienia czyszczenia historii SMS-ów
async function runCleanupSentSmsHistory() {
  return await cleanupSentSmsHistory();
}

// Funkcja do ustawienia referencji do przeglądarki puppeteer
function setScraperBrowser(browser) {
  scraperBrowser = browser;
}

// Funkcja do pobierania aktualnie zescrapowanych ID ogłoszeń
async function getCurrentScrapedAdIds() {
  return currentScrapedAdIds;
}

// Funkcja do dodawania ID ogłoszeń do listy zescrapowanych
function addScrapedAdIds(adIds = []) {
  if (Array.isArray(adIds) && adIds.length > 0) {
    // Dodaj tylko unikalne ID, które jeszcze nie istnieją w liście
    const uniqueIds = adIds.filter(id => !currentScrapedAdIds.includes(id));
    if (uniqueIds.length > 0) {
      currentScrapedAdIds = [...currentScrapedAdIds, ...uniqueIds];
      console.log(`Dodano ${uniqueIds.length} nowych ID ogłoszeń do listy zescrapowanych`);
    }
  }
  return currentScrapedAdIds.length;
}

// Funkcja do ustawiania listy zescrapowanych ID ogłoszeń
function setScrapedAdIds(adIds = []) {
  if (Array.isArray(adIds)) {
    currentScrapedAdIds = [...adIds];
    console.log(`Ustawiono listę ${currentScrapedAdIds.length} ID ogłoszeń`);
  }
  return currentScrapedAdIds.length;
}

// Funkcja do czyszczenia listy zescrapowanych ID ogłoszeń
function clearScrapedAdIds() {
  const count = currentScrapedAdIds.length;
  currentScrapedAdIds = [];
  console.log(`Wyczyszczono listę ${count} ID ogłoszeń`);
  return count;
}

// Funkcja do wznowienia przerwanych sesji scrapowania
async function resumeScrapingSession(sessionId) {
  try {
    if (isScrapingActive) {
      console.log('Scrapowanie jest już aktywne, najpierw zatrzymaj obecne zadanie');
      return {
        success: false,
        message: 'Scrapowanie jest już aktywne, najpierw zatrzymaj obecne zadanie'
      };
    }

    console.log(`Wznawianie sesji scrapowania z ID: ${sessionId}`);
    
    // Pobierz sesję z bazy danych
    const session = await prisma.scrapingSession.findUnique({
      where: { id: sessionId }
    });
    
    if (!session) {
      console.error(`Nie znaleziono sesji scrapowania z ID: ${sessionId}`);
      return {
        success: false,
        message: 'Nie znaleziono sesji scrapowania'
      };
    }
    
    // Ustaw flagi aktywności
    isScrapingActive = true;
    currentScrapingPage = session.currentPage || 1;
    
    // Wywołaj API do wznowienia sesji
    const response = await fetch(`http://localhost:${PORT}/api/scraper/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ sessionId })
    });
    
    const result = await response.json();
    console.log('Wynik wznowienia sesji scrapowania:', result);
    
    return result;
  } catch (error) {
    console.error('Błąd podczas wznawiania sesji scrapowania:', error);
    isScrapingActive = false;
    return {
      success: false,
      message: `Błąd podczas wznawiania sesji: ${error.message}`
    };
  }
}

// Eksportuj funkcje, aby były dostępne dla innych modułów
global.cronFunctions = {
  initCron,
  getCronStatus,
  updateScrapingStatus,
  runScraper,
  stopScraper,
  runCleanupSentSmsHistory,
  runSendLeaseRemindersEmail,
  setScraperBrowser,
  resumeScrapingSession,
  getCurrentScrapedAdIds,
  addScrapedAdIds,
  setScrapedAdIds,
  clearScrapedAdIds
};

app.prepare().then(() => {
  // Czyszczenie niedokończonych scrapowań przy starcie serwera
  cleanupIncompleteScrapings();
  
  // Inicjalizacja crona po uruchomieniu serwera
  initCron();
  createServer((req, res) => {
    // Parsuj URL
    const parsedUrl = parse(req.url, true);
    
    // Przekazanie zapytań do Next.js
    handle(req, res, parsedUrl);
  }).listen(PORT, (err) => {
    if (err) throw err;
    console.log(`> Serwer HTTP działa na http://localhost:${PORT}`);
  });
});
