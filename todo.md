# To do:

## Funkcjonalność wznowienia scrapowania

Przyszła funkcjonalność do zaimplementowania:
- Mo<PERSON><PERSON><PERSON><PERSON><PERSON> wznowienia przerwanego scrapowania ze strony `/scrapings`
- Implementacja systemu zapisywania stanu scrapowania (które ID zostały już pobrane, które szczegóły zostały już pobrane)
- Przechowywanie czasowego bufora ID ogłoszeń do wznowienia
- Mechanizm wznawiania scrapowania od konkretnej strony i konkretnego punktu

### Szczegóły implementacji wznowienia:

#### Statystyki scrapowania
- Zachowanie i wykorzystanie statystyk postępu (pageProgress, adsIdsProgress, adsDetailsProgress, adsWithPhonesProgress)
- Implementacja mechanizmu wznowienia na podstawie ostatniego stanu scrapowania
- Dodanie możliwości kontynuacji od ostatniego punktu z uwzględnieniem procentowego postępu

#### Obsługa trybu Turbo
- Specjalna implementacja wznowienia dla trybu Turbo, który działa w dwóch fazach:
  1. Faza zbierania ID ogłoszeń - możliwość wznowienia od konkretnej strony
  2. Faza pobierania szczegółów - możliwość wznowienia od konkretnego ID ogłoszenia
- Przechowywanie listy ID ogłoszeń, które nie zostały jeszcze przetworzone
- Optymalizacja równoległego pobierania danych przy wznowieniu

#### Interfejs użytkownika
- Aktywacja przycisku "Wznów" dla przerwanych scrapowań
- Dodanie informacji o możliwości wznowienia w interfejsie
- Wyświetlanie szacowanego czasu do zakończenia na podstawie dotychczasowego postępu
