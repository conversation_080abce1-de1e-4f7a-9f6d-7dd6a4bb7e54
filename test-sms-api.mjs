// Prosty skrypt testowy do sprawdzenia połączenia z bramką SMS
import axios from 'axios';
import https from 'https';
import fs from 'fs';
import path from 'path';
import { XMLBuilder, XMLParser } from 'fast-xml-parser';
import { fileURLToPath } from 'url';

// Uzyskaj ścieżkę bieżącego pliku dla modułów ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Konfiguracja podobna do tej z pliku config.ts
const xmlHeader = {
  transactionId: 1,
  timestamp: new Date(Date.now()).toISOString(),
  nws: 48882901100,
  MSISDN: 48606666320,
};

const xmlBuilderOptions = {
  processEntities: false,
  format: true,
  ignoreAttributes: false,
  suppressEmptyNode: true,
  arrayNodeName: 'recipient',
};

// Sprawdź czy certyfikaty istnieją
const certPath = path.join(__dirname, './src/lib/smsapi/certificates/sup-48606666320.pem');
const certExists = fs.existsSync(certPath);

// Użyj certyfikatu do komunikacji z bramką SMS
const reqHttpsAgent = certExists
  ? new https.Agent({
      rejectUnauthorized: false,
      cert: fs.readFileSync(certPath),
      key: fs.readFileSync(certPath),
    })
  : undefined;

const smsApiEndpoint = 'https://superinfo.t-mobile.pl/superinfoapi2';

async function pingSmsApi() {
  if (!reqHttpsAgent) {
    console.error('Brak certyfikatu do bramki SMS');
    return {
      status: 'error',
      message: 'Brak certyfikatu do bramki SMS. Umieść certyfikat w katalogu src/lib/smsapi/certificates/'
    };
  }

  const json = {
    'sup2:SupRequest': {
      '@_xsi:schemaLocation': 'http://superinfo.t-mobile.pl/api2 api-supera2.xsd',
      '@_xmlns:sup2': 'http://superinfo.t-mobile.pl/api2',
      '@_xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
      header: xmlHeader,
      ping: '',
    },
  };

  const xmlBuilder = new XMLBuilder(xmlBuilderOptions);
  const xmlContent = xmlBuilder.build(json);

  console.log('Wysyłanie żądania XML:');
  console.log(xmlContent);

  try {
    const response = await axios.post(smsApiEndpoint, xmlContent, {
      headers: {
        'Content-Type': 'application/xml',
      },
      httpsAgent: reqHttpsAgent,
    });

    console.log('Otrzymana odpowiedź:', response.data);

    const xmlParser = new XMLParser();
    const parsedResult = xmlParser.parse(response.data);
    return {
      status: 'ok',
      data: parsedResult['ns2:SupResponse']?.header || parsedResult
    };
  } catch (error) {
    console.error('Błąd podczas pingowania bramki SMS:', error);
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Nieznany błąd',
      details: error.response?.data || error
    };
  }
}

async function testConnection() {
  console.log('Testowanie połączenia z bramką SMS T-Mobile...');
  console.log('Używam certyfikatu:', certPath);
  console.log('Certyfikat istnieje:', certExists);
  
  try {
    const result = await pingSmsApi();
    console.log('Wynik testu:');
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Błąd podczas testu:');
    console.error(error);
  }
}

testConnection();
