const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

// Ustawienia środowiska - dev lub produkcja
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Port serwera
const PORT = parseInt(process.env.PORT || '3002', 10);

// Konfiguracja zmiennych środowiskowych dla ignorowania certyfikatu
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

app.prepare().then(() => {
  // Używamy zwykłego HTTP zamiast HTTPS, 
  // a do obsługi HTTPS będziemy używać zewnętrznego narzędzia jak mkcert lub local-ssl-proxy
  createServer((req, res) => {
    // Parsuj URL
    const parsedUrl = parse(req.url, true);
    
    // Przekazanie zapytań do Next.js
    handle(req, res, parsedUrl);
  }).listen(PORT, (err) => {
    if (err) throw err;
    console.log(`> Serwer HTTP działa na http://localhost:${PORT}`);
    console.log('> Aby używać HTTPS, użyj narzędzia lokalnego proxy SSL, np:');
    console.log('> npm install -g local-ssl-proxy');
    console.log('> local-ssl-proxy --source 3443 --target 3002');
    console.log('> Wtedy aplikacja będzie dostępna na https://localhost:3443');
  });
});
