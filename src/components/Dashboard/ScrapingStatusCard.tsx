"use client";

import { useState, useEffect } from "react";
import { ClockIcon, ReloadIcon, EnvelopeClosedIcon, PlayIcon } from "@radix-ui/react-icons";
import { getFromApi, postToApi } from "@/lib/utils/api";
import { formatDistanceToNow, format } from "date-fns";
import { pl } from "date-fns/locale";

interface CronStatus {
  enabled: boolean;
  interval: number;
  lastScrapedAt: string | null;
  isActive: boolean;
  currentScrapingPage: number | null;
  isScrapingActive: boolean;
  cleanupCronActive: boolean;
  error?: string;
}

interface ScrapingSession {
  id: number;
  scrapingUrl: string;
  startedAt: string;
  endedAt: string | null;
  status: string;
  totalPages: number;
  scrapedPages: number;
  adsFound: number;
}

export default function ScrapingStatusCard() {
  const [cronStatus, setCronStatus] = useState<CronStatus | null>(null);
  const [lastScraping, setLastScraping] = useState<ScrapingSession | null>(null);
  const [phonesToSendCount, setPhonesToSendCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [startingScrap, setStartingScrap] = useState<boolean>(false);

  // Funkcja do pobierania danych
  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Pobierz status scrapera
      const cronResponse = await getFromApi('/api/cron');
      if (cronResponse.ok) {
        const cronData = await cronResponse.json();
        setCronStatus(cronData);
      }
      
      // Pobierz ostatnią sesję scrapowania
      const scrapingsResponse = await getFromApi('/api/scrapings');
      if (scrapingsResponse.ok) {
        const scrapingsData = await scrapingsResponse.json();
        if (scrapingsData && scrapingsData.length > 0) {
          setLastScraping(scrapingsData[0]); // Pierwsza sesja to najnowsza (sortowane desc)
        }
      }
      
      // Pobierz liczbę telefonów do wysyłki
      const phonesResponse = await getFromApi('/api/phones-to-send');
      if (phonesResponse.ok) {
        const phonesData = await phonesResponse.json();
        setPhonesToSendCount(phonesData.length || 0);
      }
    } catch (error) {
      console.error("Błąd podczas pobierania danych:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    // Odświeżaj dane co 30 sekund
    const interval = setInterval(fetchData, 30000);
    
    return () => clearInterval(interval);
  }, []);
  
  // Formatowanie daty ostatniego scrapowania
  const formatLastScrapingDate = (date: string) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true,
        locale: pl
      });
    } catch (error) {
      return "nieznana data";
    }
  };

  // Formatowanie dokładnej daty i godziny
  const formatExactDateTime = (date: string) => {
    try {
      return format(new Date(date), 'dd.MM.yyyy, HH:mm:ss', { locale: pl });
    } catch (error) {
      return "nieznana data";
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-8">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">Status systemu</h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
        {/* Status scrapowania */}
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Status scrapowania</h3>
            {loading ? (
              <ReloadIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 animate-spin" />
            ) : (
              <div className={`h-3 w-3 rounded-full ${cronStatus?.isScrapingActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            )}
          </div>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {loading ? (
              "Ładowanie..."
            ) : cronStatus?.isScrapingActive ? (
              <span className="flex items-center text-green-600 dark:text-green-400">
                <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
                Aktywne
                ({cronStatus.currentScrapingPage && ` (strona ${cronStatus.currentScrapingPage})`})
              </span>
            ) : (
              <span className="text-gray-600 dark:text-gray-400">Nieaktywne</span>
            )}
          </p>
        </div>
        
        {/* Ostatnie scrapowanie */}
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Ostatnie scrapowanie</h3>
            <ClockIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
          </div>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {loading ? (
              "Ładowanie..."
            ) : lastScraping ? (
              formatLastScrapingDate(lastScraping.startedAt)
            ) : (
              "Brak danych"
            )}
          </p>
          {lastScraping && (
            <>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {lastScraping.status === "completed" ? (
                  `Znaleziono ${lastScraping.adsFound} ogłoszeń`
                ) : lastScraping.status === "in_progress" ? (
                  "W trakcie"
                ) : (
                  "Przerwane"
                )}
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                {formatExactDateTime(lastScraping.startedAt)}
              </p>
            </>
          )}
        </div>
        
        {/* SMS-y do wysłania */}
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">SMS-y do wysłania</h3>
            <EnvelopeClosedIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
          </div>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {loading ? (
              "Ładowanie..."
            ) : (
              phonesToSendCount
            )}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {phonesToSendCount === 1 ? "wiadomość" : 
             phonesToSendCount > 1 && phonesToSendCount < 5 ? "wiadomości" : "wiadomości"}
          </p>
        </div>
        
      </div>
    </div>
  );
}
