"use client";

import { useState, useEffect, useRef } from "react";
import { MagnifyingGlassIcon, Cross2Icon } from "@radix-ui/react-icons";
import { cn } from "@/lib/utils";
import { Kbd } from "@radix-ui/themes";

interface Ad {
  id: number;
  brandModel: string;
  price?: string;
  year?: string;
  mileage?: string;
  phones: { id: number; number: string }[];
  url?: string;
  smsSent?: boolean;
}

export default function GlobalSearch() {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimeout = useRef<NodeJS.Timeout>();
  
  // Obsługa skrótu klawiszowego Ctrl+K
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+K lub Cmd+K dla Mac
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setIsOpen(true);
      }
      
      // Obsługa klawisza Escape
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);
  
  useEffect(() => {
    // Focus na input po otwarciu modalu
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);
  
  useEffect(() => {
    // Obsługa kliknięcia poza modalem
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);
  
  useEffect(() => {
    // Wyszukiwanie z debounce
    if (query.length >= 3) {
      setLoading(true);
      
      // Usuń spacje z zapytania
      const cleanQuery = query.replace(/\s+/g, "");
      
      // Wyczyść poprzedni timeout
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
      
      // Utwórz nowy timeout
      debounceTimeout.current = setTimeout(async () => {
        try {
          // Dodajemy parametr searchBy=phone, aby priorytetyzować wyszukiwanie po numerze telefonu
          const response = await fetch(`/api/ads?search=${encodeURIComponent(cleanQuery)}&searchBy=phone&limit=4&page=1`);
          if (response.ok) {
            const data = await response.json();
            // Sortujemy wyniki - ogłoszenia z wysłanymi SMS-ami na górze
            const sortedAds = [...data.ads].sort((a, b) => {
              // Jeśli jedno ma SMS wysłany a drugie nie, to to z wysłanym idzie na górę
              if (a.smsSent && !b.smsSent) return -1;
              if (!a.smsSent && b.smsSent) return 1;
              return 0;
            });
            setResults(sortedAds);
          }
        } catch (error) {
          console.error("Błąd wyszukiwania:", error);
        } finally {
          setLoading(false);
        }
      }, 300);
    } else {
      setResults([]);
      setLoading(false);
    }
    
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, [query]);
  
  const handleToggleSearch = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setQuery("");
      setResults([]);
    }
  };
  
  return (
    <div className="relative">
      <button
        onClick={handleToggleSearch}
        className="p-2 rounded-md hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring text-muted-foreground hover:text-foreground flex items-center gap-2"
        aria-label="Wyszukiwanie"
        title="Wyszukiwanie (Ctrl+K)"
      >
        <MagnifyingGlassIcon className="h-5 w-5" />
        <span className="inline-flex items-center rounded border border-gray-200 bg-gray-100 px-1.5 py-0.5 text-xs font-semibold text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200">
          CTRL + K
        </span>
      </button>
      
      {isOpen && (
        <div 
          ref={modalRef}
          className="fixed inset-x-0 top-24 mx-auto w-full max-w-2xl bg-card shadow-2xl rounded-md border border-border z-50 overflow-hidden"
        >
          <div className="p-4 border-b border-border flex items-center">
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Szukaj ogłoszeń (min. 3 znaki)"
              className="flex-1 bg-transparent border-none focus:outline-none focus:ring-0 placeholder:text-muted-foreground"
            />
            <div className="text-xs text-muted-foreground mr-2">ESC aby zamknąć</div>
            {query && (
              <button
                onClick={() => setQuery("")}
                className="p-1 rounded-full hover:bg-accent text-muted-foreground hover:text-foreground"
              >
                <Cross2Icon className="h-4 w-4" />
              </button>
            )}
          </div>
          
          <div className={cn("max-h-[calc(8*5rem)] overflow-y-auto", (!query || query.length < 3) && "hidden")}>
            {loading ? (
              <div className="flex justify-center items-center p-6">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : results.length > 0 ? (
              <div>
                {results.map((ad) => (
                  <a 
                    key={ad.id} 
                    href={ad.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="block p-3 border-b border-border hover:bg-accent/30"
                  >
                    <div className="font-medium truncate">{ad.brandModel}</div>
                    <div className="flex justify-between items-center mt-1">
                      <div className="text-sm text-muted-foreground">
                        {ad.year} {ad.mileage && `• ${ad.mileage}`}
                      </div>
                      <div className="text-sm font-medium">{ad.price}</div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      {ad.phones && ad.phones.length > 0 && (
                        <div className="text-sm text-muted-foreground">
                          Nr tel: {ad.phones[0].number}
                        </div>
                      )}
                      <div className={`text-xs px-2 py-0.5 rounded-full ${ad.smsSent ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'}`}>
                        {ad.smsSent ? 'SMS wysłany' : 'SMS niewysłany'}
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            ) : query.length >= 3 ? (
              <div className="p-6 text-center text-muted-foreground">
                Nie znaleziono ogłoszeń
              </div>
            ) : null}
          </div>
          
          {(query.length > 0 && query.length < 3) && (
            <div className="p-6 text-center text-muted-foreground">
              Wpisz minimum 3 znaki aby wyszukać
            </div>
          )}
        </div>
      )}
    </div>
  );
}
