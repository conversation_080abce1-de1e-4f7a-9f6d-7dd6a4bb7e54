"use client";

import { useState, useEffect } from "react";
import { ReloadIcon, StopIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";

interface CronStatus {
  enabled: boolean;
  interval: number;
  lastScrapedAt: string | null;
  isActive: boolean;
  currentScrapingPage: number | null;
  isScrapingActive: boolean;
  cleanupCronActive: boolean;
  error?: string;
}

export default function ScraperStatus() {
  const [status, setStatus] = useState<CronStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [stoppingScraper, setStoppingScraper] = useState(false);

  // Pobierz status crona
  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/cron");
      
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        console.error("Nie udało się pobrać statusu scrapera");
      }
    } catch (error) {
      console.error("Błąd podczas pobierania statusu scrapera:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    
    // Odświeżaj status co 5 sekund
    const interval = setInterval(fetchStatus, 5000);
    
    return () => clearInterval(interval);
  }, []);

  if (loading || !status) {
    return null;
  }

  // Jeśli scraping nie jest aktywny, nie pokazuj nic
  if (!status.isScrapingActive) {
    return null;
  }

  // Funkcja do zatrzymania scrapera
  const handleStopScraper = async () => {
    try {
      setStoppingScraper(true);
      
      const response = await fetch("/api/cron", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          stopScraping: true
        }),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zatrzymać scrapera");
      }
      
      // Odśwież status po zatrzymaniu
      await fetchStatus();
    } catch (error) {
      console.error("Błąd podczas zatrzymywania scrapera:", error);
    } finally {
      setStoppingScraper(false);
    }
  };

  return (
    <div className="flex items-center px-2 py-1 rounded-md bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200">
      <ReloadIcon className="h-4 w-4 mr-2 animate-spin" />
      <span className="text-xs mr-2">
        Scrapowanie aktywne
        ({status.currentScrapingPage && ` (strona ${status.currentScrapingPage})`})
      </span>
      <Button 
        variant="destructive" 
        size="sm" 
        className="h-6 px-2 py-0 ml-2 text-xs"
        onClick={handleStopScraper}
        disabled={stoppingScraper}
      >
        <StopIcon className="h-3 w-3 mr-1" />
        Stop
      </Button>
    </div>
  );
}
