"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import GlobalSearch from "@/components/GlobalSearch";
import SmsStatus from "./SmsStatus";
import ScraperStatus from "./ScraperStatus";
import { useToast } from "@/components/ui/toast";
import { 
  HamburgerMenuIcon, 
  Cross1Icon, 
  BarChartIcon, 
  FileTextIcon, 
  GearIcon, 
  CodeIcon, 
  MobileIcon, 
  EnvelopeClosedIcon,
  ExclamationTriangleIcon,
  ExternalLinkIcon,
  ChatBubbleIcon,
  Cross2Icon,
  ClockIcon
} from "@radix-ui/react-icons";
import { cn } from "@/lib/utils";
import { getFromApi } from "@/lib/utils/api";

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  count?: number;
  target?: string;
  isHeader?: boolean;
  onClick?: () => void;
}

export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [adsCount, setAdsCount] = useState<number>(0);
  const [phonesToSendCount, setPhonesToSendCount] = useState<number>(0);
  const [todayRemindersCount, setTodayRemindersCount] = useState<number>(0);
  const pathname = usePathname();
  const { hideToast, showToast } = useToast();

  // Obsługa zapisanego ID toasta z błędem
  useEffect(() => {
    const toastId = localStorage.getItem('smsErrorToastId');
    
    // Jeśli istnieje zapisane ID błędu, upewnij się, że toast jest widoczny
    if (toastId) {
      // Obserwuj czy toast został zamknięty (przez przycisk X)
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
            // Jeśli toast został usunięty, usuń też ID z localStorage
            localStorage.removeItem('smsErrorToastId');
            observer.disconnect();
          }
        });
      });
      
      // Rozpocznij obserwację kontenera toastów
      const toastContainer = document.querySelector('.toast-container');
      if (toastContainer) {
        observer.observe(toastContainer, { childList: true, subtree: true });
      }
      
      return () => {
        observer.disconnect();
      };
    }
  }, []);

  // Sprawdź, czy użytkownik jest zalogowany
  useEffect(() => {
    // Jeśli jesteśmy już na stronie logowania, nie przekierowuj
    if (pathname === "/login") {
      return;
    }
    
    const token = localStorage.getItem("token");
    if (!token) {
      console.log("Brak tokenu, przekierowuję na stronę logowania");
      window.location.href = "/login";
    }
  }, [pathname]);

  // Funkcja do wylogowania
  const handleLogout = () => {
    // Wyczyść dane logowania
    localStorage.removeItem("token");
    document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    
    // Pokaż komunikat o pomyślnym wylogowaniu
    showToast({
      message: "Wylogowano pomyślnie",
      type: "success",
      duration: 3000
    });
    
    // Bezpośrednie przekierowanie na stronę logowania
    window.location.href = "/login";
  };

  // Pobierz liczby ogłoszeń, numerów do wysyłki i przypomnień na dzisiaj
  useEffect(() => {
    const fetchCounts = async () => {
      try {
        // Pobierz liczbę ogłoszeń
        const adsResponse = await getFromApi('/api/ads?page=1&limit=1');
        if (adsResponse.ok) {
          const adsData = await adsResponse.json();
          setAdsCount(adsData.pagination?.total || 0);
        }

        // Pobierz liczbę numerów telefonów do wysyłki
        const phonesResponse = await getFromApi('/api/phones-to-send');
        if (phonesResponse.ok) {
          const phonesData = await phonesResponse.json();
          setPhonesToSendCount(phonesData.pagination?.total || 0);
        }
        
        // Pobierz liczbę przypomnień o leasingu na dzisiaj
        const remindersResponse = await getFromApi('/api/lease-reminders?countToday=true');
        if (remindersResponse.ok) {
          const remindersData = await remindersResponse.json();
          setTodayRemindersCount(remindersData.count || 0);
        }
      } catch (error) {
        console.error("Błąd podczas pobierania danych:", error);
      }
    };

    fetchCounts();
  }, []);

  const navigation: NavItem[] = [
    { 
      name: "Ekran główny", 
      href: "/dashboard", 
      icon: <BarChartIcon className="w-5 h-5" /> 
    },
    { 
      name: "Lista ogłoszeń", 
      href: "/ads", 
      icon: <FileTextIcon className="w-5 h-5" />,
      count: adsCount 
    },
    { 
      name: "Scrapowanie i historia", 
      href: "/scrapings", 
      icon: <ClockIcon className="w-5 h-5" /> 
    },
    { 
      name: "SMS", 
      href: "#", 
      icon: null,
      isHeader: true
    },
    { 
      name: "Wysyłka", 
      href: "/phone-list", 
      icon: <MobileIcon className="w-5 h-5" />,
      count: phonesToSendCount 
    },
    { 
      name: "Blokowane numery", 
      href: "/blocked-list", 
      icon: <ExclamationTriangleIcon className="w-5 h-5" /> 
    },
    { 
      name: "Karencja", 
      href: "/sms-quarantine", 
      icon: <ClockIcon className="w-5 h-5" /> 
    },
    { 
      name: "USTAWIENIA", 
      href: "#", 
      icon: null,
      isHeader: true
    },
    { 
      name: "Ogólne", 
      href: "/settings", 
      icon: <GearIcon className="w-5 h-5" /> 
    },
    { 
      name: "Zaawansowane", 
      href: "/scraper-settings", 
      icon: <ChatBubbleIcon className="w-5 h-5" /> 
    },
    { 
      name: "Mapowanie selektorów", 
      href: "/selectors", 
      icon: <CodeIcon className="w-5 h-5" /> 
    },
    // Dodajemy sekcję LEASING
    { 
      name: "LEASING CRM",
      href: "#",
      icon: null,
      isHeader: true
    },
    { 
      name: "Wygasające", 
      href: "/przypomnienia", 
      icon: <ClockIcon className="w-5 h-5" />,
      count: todayRemindersCount
    },
    // Dodajemy sekcję INNE przed Bramką
    { 
      name: "INNE",
      href: "#",
      icon: null,
      isHeader: true
    },
    { 
      name: "Logi", 
      href: "/logs", 
      icon: <FileTextIcon className="w-5 h-5" /> 
    },
    { 
      name: "Bramka T-Mobile", 
      href: "https://superinfo.t-mobile.pl/start/auth/userauth?execution=1", 
      icon: (
        <div className="flex items-center">
          <EnvelopeClosedIcon className="w-5 h-5" />
        </div>
      ),
      target: "_blank"
    },
    { 
      name: "Wyloguj", 
      href: "#", 
      icon: <Cross2Icon className="w-5 h-5" />,
      onClick: handleLogout
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? "block" : "hidden"}`}>
        <div 
          className="fixed inset-0 transition-opacity bg-background/80 backdrop-blur-sm" 
          onClick={() => setSidebarOpen(false)}
        />
        <div className="relative flex flex-col flex-1 w-full max-w-xs transition-all transform bg-card">
          <div className="absolute top-0 right-0 pt-2 -mr-12">
            <button
              type="button"
              className="flex items-center justify-center w-10 h-10 ml-1 rounded-full focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              onClick={() => setSidebarOpen(false)}
            >
              <span className="sr-only">Zamknij sidebar</span>
              <Cross1Icon className="w-6 h-6 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <span className="text-xl font-bold">OtoScraper</span>
            </div>
            <nav className="px-2 mt-5 space-y-1">
              {navigation.map((item) => {
                if (item.isHeader) {
                  return (
                    <div 
                      key={item.name}
                      className="text-xs font-semibold text-muted-foreground uppercase tracking-[2px] pt-4 pb-2 px-2"
                    >
                      <div className="flex items-center">
                        {item.icon && <span className="mr-2">{item.icon}</span>}
                        {item.name}
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      target={item.target}
                      onClick={item.onClick}
                      className={cn(
                        "group flex items-center px-2 py-2 text-base font-medium rounded-md",
                        pathname === item.href
                          ? "bg-accent text-accent-foreground"
                          : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
                      )}
                    >
                      <div className="flex-shrink-0 mr-4 text-muted-foreground">
                        {item.icon}
                      </div>
                      <div className="flex items-center justify-between flex-1">
                        {item.name}
                        {item.count !== undefined && item.count > 0 && (
                          <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-primary text-primary-foreground">
                            {item.count}
                          </span>
                        )}
                      </div>
                    </Link>
                  );
                }
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Static sidebar for desktop */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div className="flex flex-col flex-1 min-h-0 border-r bg-card border-border">
          <div className="border-b border-border">
            <SmsStatus />
          </div>
          <div className="flex flex-col flex-1 pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <span className="text-xl font-bold">OtoScraper</span>
            </div>
            <nav className="flex-1 px-2 mt-5 space-y-1">
              {navigation.map((item) => {
                if (item.isHeader) {
                  return (
                    <div 
                      key={item.name}
                      className="text-xs font-semibold text-muted-foreground uppercase tracking-[2px] pt-4 pb-2 px-2"
                    >
                      <div className="flex items-center">
                        {item.icon && <span className="mr-2">{item.icon}</span>}
                        {item.name}
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      target={item.target}
                      onClick={item.onClick}
                      className={cn(
                        "group flex items-center px-2 py-2 text-sm font-medium rounded-md",
                        pathname === item.href
                          ? "bg-accent text-accent-foreground"
                          : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
                      )}
                    >
                      <div className="flex-shrink-0 mr-3 text-muted-foreground">
                        {item.icon}
                      </div>
                      <div className="flex items-center justify-between flex-1">
                        {item.name}
                        {item.count !== undefined && item.count > 0 && (
                          <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-primary text-primary-foreground">
                            {item.count}
                          </span>
                        )}
                      </div>
                    </Link>
                  );
                }
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 md:pl-64">
        <div className="sticky top-0 z-10 pt-1 pl-1 shadow md:hidden sm:pl-3 sm:pt-3 bg-card">
          <button
            type="button"
            className="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-muted-foreground hover:text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Otwórz sidebar</span>
            <HamburgerMenuIcon className="w-6 h-6" />
          </button>
        </div>
        <main className="flex-1">
            <div className="main-container">
              <div className="flex items-center justify-end mb-4 space-x-2">
                <GlobalSearch />
                <ScraperStatus />
                <ThemeToggle />
              </div>
              {children}
            </div>
        </main>
      </div>
    </div>
  );
}
