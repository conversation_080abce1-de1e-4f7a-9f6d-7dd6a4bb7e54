"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { CircleAlert } from "lucide-react";
import { useToast } from "@/components/ui/toast";
import { getFromApi } from "@/lib/utils/api";

export default function SmsStatus() {
  const [status, setStatus] = useState<'loading' | 'error' | 'ok'>('loading');
  const [message, setMessage] = useState<string>("");
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const { showToast } = useToast();

  // Przechowujemy informację, czy już sprawdziliśmy status
  const hasCheckedRef = React.useRef(false);
  
  useEffect(() => {
    // Sprawdź, czy status był już sprawdzany w tej sesji
    if (hasCheckedRef.current) {
      return;
    }
    
    // Oznacz, że sprawdziliśmy status
    hasCheckedRef.current = true;
    
    const checkSmsStatus = async () => {
      try {
        setStatus('loading');
        const response = await getFromApi('/api/sms/status');
        const data = await response.json();

        if (data.status === 'ok') {
          setStatus('ok');
          setMessage("Połączono z bramką SMS");
        } else {
          setStatus('error');
          
          // Uproszczony komunikat o błędzie - szczegóły będą w toaście
          setMessage("Błąd komunikacji z bramką");
          
          // Jeśli jest szczegółowy błąd, wyświetl go w toaście
          if (data.message) {
            // Zapisujemy ID toasta, aby móc go zamknąć później
            const toastId = showToast({
              message: data.message,
              type: "error",
              // Bez automatycznego zamykania
              duration: 0
            });
            
            // Zapisujemy ID toasta do zamknięcia
            localStorage.setItem('smsErrorToastId', toastId);
          }
        }
      } catch (error) {
        setStatus('error');
        setMessage("Błąd komunikacji z bramką");
        
        // Wyświetl błąd w toaście
        const toastId = showToast({
          message: error instanceof Error ? error.message : "Wystąpił nieznany błąd podczas łączenia z bramką SMS",
          type: "error",
          // Bez automatycznego zamykania
          duration: 0
        });
        
        // Zapisujemy ID toasta do zamknięcia
        localStorage.setItem('smsErrorToastId', toastId);
      }
    };

    checkSmsStatus();
    
    // Sprawdzaj status co 5 minut
    const interval = setInterval(checkSmsStatus, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Automatycznie ukryj powiadomienie po 10 sekundach, jeśli status = ok
  useEffect(() => {
    if (status === 'ok') {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 10000);
      
      return () => clearTimeout(timer);
    }
  }, [status]);

  if (!isVisible) return null;

  return (
    <div 
      className={`px-4 py-2 flex items-center justify-between ${
        status === 'ok' 
          ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300' 
          : status === 'error' 
            ? 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-300'
            : 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
      }`}
    >
      <div className="flex items-center">
        {status === 'loading' ? (
          <div className="animate-spin h-4 w-4 border-b-2 border-current rounded-full mr-2" />
        ) : status === 'error' ? (
          <CircleAlert className="h-4 w-4 mr-2" />
        ) : (
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )}
        <span className="text-xs font-medium">
          {message}
        </span>
      </div>
      
      <button 
        type="button"
        onClick={() => setIsVisible(false)}
        className="text-sm font-medium focus:outline-none"
      >
        <span className="sr-only">Zamknij</span>
        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  );
}
