"use client";

import * as React from "react";

export interface ToastProps {
  message: string;
  type: "success" | "error" | "info" | "warning";
  onClose?: () => void;
  duration?: number;
}

/**
 * Komponent Toast
 */
export function Toast({ message, type, onClose, duration = 5000 }: ToastProps) {
  React.useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        if (onClose) onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const bgColor = React.useMemo(() => {
    switch (type) {
      case "success":
        return "bg-green-100 border-green-400 text-green-700 dark:bg-green-800/20 dark:border-green-600 dark:text-green-400";
      case "error":
        return "bg-red-100 border-red-400 text-red-700 dark:bg-red-800/20 dark:border-red-600 dark:text-red-400";
      case "warning":
        return "bg-yellow-100 border-yellow-400 text-yellow-700 dark:bg-yellow-800/20 dark:border-yellow-600 dark:text-yellow-400";
      case "info":
      default:
        return "bg-blue-100 border-blue-400 text-blue-700 dark:bg-blue-800/20 dark:border-blue-600 dark:text-blue-400";
    }
  }, [type]);

  return (
    <div className={`px-4 py-3 rounded border ${bgColor} relative`}>
      <span className="block sm:inline">{message}</span>
      {onClose && (
        <button
          onClick={onClose}
          className="relative ml-2"
          aria-label="Zamknij"
        >
          <span className="text-xl">&times;</span>
        </button>
      )}
    </div>
  );
}

/**
 * Kontekst do zarządzania toastami w aplikacji
 */
const ToastContext = React.createContext<{
  showToast: (props: Omit<ToastProps, 'onClose'>) => string;
  hideToast: (id?: string) => void;
}>({
  showToast: () => "",
  hideToast: () => {},
});

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<Array<ToastProps & { id: string }>>([]);

  const showToast = React.useCallback((props: Omit<ToastProps, 'onClose'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts((prev) => [...prev, { ...props, id, onClose: () => hideToast(id) }]);
    return id;
  }, []);

  const hideToast = React.useCallback((id?: string) => {
    if (id) {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    } else {
      setToasts([]);
    }
  }, []);

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      <div className="toast-container fixed bottom-4 right-4 z-50 space-y-2 max-w-md">
        {toasts.map((toast) => (
          <div key={toast.id} className="animate-fade-in">
            <Toast {...toast} />
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
}

export function useToast() {
  const context = React.useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}
