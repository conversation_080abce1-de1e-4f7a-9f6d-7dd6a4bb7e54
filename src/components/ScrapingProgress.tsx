"use client";

import { useState, useEffect } from "react";
import { getFromApi } from "@/lib/utils/api";

interface ScrapingSession {
  id: number;
  startedAt: string;
  finishedAt: string | null;
  totalPages: number;
  currentPage: number;
  status: string;
  adsIdsScraped: number;
  adsDetailsScraped: number;
  adsWithPhones: number;
  totalAdsIds: number;
  scrapingUrl: string;
  createdAt: string;
  updatedAt: string;
  pausedAt?: string | null;
  pauseDuration?: number | null;
  pauseReason?: string | null;
  // Dodatkowe pola obliczone na serwerze
  pageProgress?: number;
  adsIdsProgress?: number;
  adsDetailsProgress?: number;
  adsWithPhonesProgress?: number;
}

// Formatowanie procentów z obsługą wartości undefined
const formatPercent = (value?: number) => {
  if (value === undefined) return "0%";
  return `${value}%`;
};

export default function ScrapingProgress() {
  const [session, setSession] = useState<ScrapingSession | null>(null);
  
  const fetchSession = async () => {
    try {
      const response = await getFromApi('/api/scrapings/current');
      
      if (response.ok) {
        const data = await response.json();
        if (data && data.id) {
          setSession(data);
        }
      }
    } catch (error) {
      console.error('Błąd podczas pobierania aktualnej sesji scrapowania:', error);
    }
  };
  
  useEffect(() => {
    fetchSession();
    
    // Automatyczne odświeżanie co 1 sekundę, jeśli sesja jest aktywna
    const intervalId = setInterval(() => {
      fetchSession();
    }, 1000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  if (!session || (session.status !== 'in_progress' && session.status !== 'paused')) return null;
  
  return (
    <div className="space-y-4">
      <div>
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Strony</span>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {session.currentPage} / {session.totalPages}
            <span className="ml-2 text-gray-500">({formatPercent(session.pageProgress)})</span>
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div 
            className="bg-blue-600 h-2.5 rounded-full" 
            style={{ width: `${session.pageProgress || 0}%` }}
          ></div>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">ID ogłoszeń</span>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {session.adsIdsScraped} / {session.totalAdsIds || '?'}
            <span className="ml-2 text-gray-500">({formatPercent(session.adsIdsProgress)})</span>
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div 
            className="bg-blue-600 h-2.5 rounded-full" 
            style={{ width: `${session.adsIdsProgress || 0}%` }}
          ></div>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Szczegóły ogłoszeń</span>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {session.adsDetailsScraped} / {session.totalAdsIds || '?'}
            <span className="ml-2 text-gray-500">({formatPercent(session.adsDetailsProgress)})</span>
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div 
            className="bg-blue-600 h-2.5 rounded-full" 
            style={{ width: `${session.adsDetailsProgress || 0}%` }}
          ></div>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Numery telefonów</span>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {session.adsWithPhones} / {session.totalAdsIds || '?'}
            <span className="ml-2 text-gray-500">({formatPercent(session.adsWithPhonesProgress)})</span>
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div 
            className="bg-blue-600 h-2.5 rounded-full" 
            style={{ width: `${session.adsWithPhonesProgress || 0}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
}
