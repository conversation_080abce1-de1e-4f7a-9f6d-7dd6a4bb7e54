import { NextRequest, NextResponse } from 'next/server';
import { getTokenFromHeader, verifyToken } from './lib/auth/jwt';

// Ścieżki, które nie wymagają uwierzytelnienia
const publicPaths = [
  '/api/auth/login',
  '/api/stats', // Dodajemy endpoint stats do publicznych ścieżek
  '/api/scraper', // Dodajemy endpoint scraper do publicznych ścieżek
  '/api/cron', // Dodajemy endpoint cron do publicznych ścieżek
  '/login',
  '/_next',
  '/favicon.ico',
  '/static',
  '/images',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Sprawdź, czy ścieżka jest publiczna
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }
  
  // Pobierz token z nagłówka i z ciasteczek
  const authHeader = request.headers.get('authorization');
  const headerToken = getTokenFromHeader(authHeader || '');
  const cookieToken = request.cookies.get('token')?.value;
  const authCookieToken = request.cookies.get('auth_token')?.value;
  
  // Użyj tokenu z nagłówka lub z ciasteczek
  const token = headerToken || cookieToken || authCookieToken;
  
  // Ograniczone logowanie tylko dla ścieżki
  // console.log(`[Middleware] Ścieżka: ${pathname}`);
  
  // Jeśli nie ma tokenu, spróbuj odczytać go z query string (dla celów debugowania)
  let tokenFromQuery = null;
  try {
    tokenFromQuery = request.nextUrl.searchParams.get('token');
    if (tokenFromQuery) {
      // Token znaleziony w query string - bez logowania szczegółów
    }
  } catch (e) {
    console.log('[Middleware] Błąd przy odczytywaniu tokenu z query string', e);
  }
  
  // Użyj tokenu z dowolnego źródła
  const finalToken = token || tokenFromQuery;
  
  // Jeśli nie ma tokenu
  if (!finalToken) {
    
    // Dla żądań API, zwróć błąd 401
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({ error: 'Brak tokenu uwierzytelniającego' }),
        { status: 401, headers: { 'content-type': 'application/json' } }
      );
    }
    
    // Dla stron, przekieruj na stronę logowania
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  try {
    // Sprawdź, czy token jest ważny - bez logowania tokenu
    const payload = await verifyToken(finalToken);
    
    if (!payload) {
      throw new Error('Token jest nieprawidłowy');
    }
    
    // console.log(`[Middleware] Uwierzytelnianie udane`);
    
    // Dodaj informacje o użytkowniku do żądania
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-user-id', payload.userId.toString());
    requestHeaders.set('x-user-email', payload.email);
    
    // Kontynuuj z przetwarzaniem żądania
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  } catch (error) {
    console.error('[Middleware] Nieprawidłowy token uwierzytelniający');
    
    // Dla żądań API, zwróć błąd 401
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({ error: 'Nieprawidłowy token uwierzytelniający' }),
        { status: 401, headers: { 'content-type': 'application/json' } }
      );
    }
    
    // Dla stron, przekieruj na stronę logowania
    return NextResponse.redirect(new URL('/login', request.url));
  }
}

export const config = {
  matcher: [
    // Wszystkie ścieżki z wyjątkiem statycznych plików
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
