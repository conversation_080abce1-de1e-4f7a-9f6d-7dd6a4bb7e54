import * as jose from 'jose';

// Sekret do podpisywania tokenów JWT - u<PERSON><PERSON>wamy stałej warto<PERSON>
const JWT_SECRET = 'otoscraper-secret-key-do-not-change';
// Przekształcamy klucz na format akceptowany przez jose - u<PERSON><PERSON>wamy surowego bufora dla klucza
const SECRET = new Uint8Array(Buffer.from(JWT_SECRET, 'utf-8'));

// <PERSON><PERSON> wa<PERSON> tokenu (24 godziny)
const JWT_EXPIRES_IN = 24 * 60 * 60; // w sekundach

export interface JwtPayload {
  userId: number;
  email: string;
  iat?: number;
  exp?: number;
}

/**
 * Generuje token JWT dla użytkownika
 */
export async function generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): Promise<string> {
  try {
    return await new jose.SignJWT({
      userId: payload.userId,
      email: payload.email
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(`${JWT_EXPIRES_IN}s`)
      .sign(SECRET);
  } catch (error) {
    console.error('[JWT] Błąd generowania tokenu');
    throw new Error('Nie można wygenerować tokenu JWT');
  }
}

/**
 * Weryfikuje token JWT
 */
export async function verifyToken(token: string): Promise<JwtPayload | null> {
  try {
    const { payload } = await jose.jwtVerify(token, SECRET);
    
    // Sprawdź, czy payload zawiera wymagane pola
    if (!payload || typeof payload !== 'object' || !('userId' in payload) || !('email' in payload)) {
      console.error('[JWT] Payload nie zawiera wymaganych pól');
      return null;
    }
    
    return {
      userId: Number(payload.userId),
      email: String(payload.email),
      iat: payload.iat,
      exp: payload.exp
    };
  } catch (error) {
    console.error('[JWT] Błąd weryfikacji tokenu');
    return null;
  }
}

/**
 * Pobiera token z nagłówka Authorization
 */
export function getTokenFromHeader(authHeader?: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  return authHeader.split(' ')[1];
}
