/**
 * <PERSON><PERSON>ł zarządzający ID ogłoszeń w trybie turbo
 * Odpowiada za przechowywanie, aktualizowanie i weryfikację ID ogłoszeń
 */

// Funkcja do synchronizacji ID ogłoszeń z globalnym stanem
export function syncWithGlobalState() {
  if (global.cronFunctions && typeof global.cronFunctions.getCurrentScrapedAdIds === 'function') {
    try {
      // @ts-ignore - Ignorujemy błąd TypeScript, bo sprawdzamy dostępność funkcji w czasie wykonania
      return global.cronFunctions.getCurrentScrapedAdIds();
    } catch (error) {
      console.error('Błąd podczas synchronizacji ID ogłoszeń z globalnym stanem:', error);
    }
  }
  return [];
}

// Funkcja do dodawania nowych ID do globalnego stanu
export function addToGlobalState(adIds: string[]) {
  if (!adIds || adIds.length === 0) return 0;
  
  if (global.cronFunctions && typeof global.cronFunctions.addScrapedAdIds === 'function') {
    try {
      // @ts-ignore - Ignorujemy błąd TypeScript, bo sprawdzamy dostępność funkcji w czasie wykonania
      return global.cronFunctions.addScrapedAdIds(adIds);
    } catch (error) {
      console.error('Błąd podczas dodawania ID ogłoszeń do globalnego stanu:', error);
    }
  }
  return 0;
}

// Funkcja do filtrowania nowych ID (pomijająca te, które już były przetwarzane)
export function filterNewIds(allIds: string[], processedIds: string[]): string[] {
  if (!processedIds || processedIds.length === 0) return allIds;
  if (!allIds || allIds.length === 0) return [];
  
  // Tworzymy zbiór (Set) z przetworzonych ID dla szybszego wyszukiwania
  const processedIdSet = new Set(processedIds);
  
  // Usuń duplikaty z listy wszystkich ID
  const uniqueAllIds = [...new Set(allIds)];
  
  // Zwraca tylko te ID, których nie ma w zbiorze przetworzonych
  return uniqueAllIds.filter(id => !processedIdSet.has(id));
}

// Funkcja do batchowego przetwarzania ID (podzielone na mniejsze części)
export function createBatches(ids: string[], batchSize: number = 10): string[][] {
  const batches: string[][] = [];
  
  for (let i = 0; i < ids.length; i += batchSize) {
    batches.push(ids.slice(i, i + batchSize));
  }
  
  return batches;
}
