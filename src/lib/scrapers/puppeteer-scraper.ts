import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { PrismaClient, ScrapingSession } from '@prisma/client';
import * as libphonenumber from 'google-libphonenumber';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { CheerioHelper } from './cheerio-helper';

const prisma = new PrismaClient();
const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();

interface ScraperSettings {
  headless: boolean;
  slowMo: number;
  userAgentRotation: boolean;
  proxyEnabled: boolean;
  proxyUrl?: string;
  maxPages?: number; // Maksymalna liczba stron do scrapowania (-1 = wszystkie)
  turboMode?: boolean; // Inteligentny tryb turbo (eksperymentalne)
  allowedRegions?: string[]; // Lista dozwolonych województw
}

interface AdData {
  externalId: string;
  brandModel: string;
  desc?: string;
  date?: string;
  location?: string;
  mileage?: string;
  year?: string;
  price?: string;
  url?: string;
  phones: string[];
  // Dodaje pola pomocnicze do korzystania z API
  apiData?: Record<string, unknown> | null;
}

export class PuppeteerScraper {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private settings: ScraperSettings;

  // Rate limiting dla Gemini API
  private geminiRequestQueue: Array<{ locationText: string; resolve: (value: string | null) => void; reject: (error: Error) => void }> = [];
  private geminiRequestCount = 0;
  private geminiLastMinute = 0;
  private geminiProcessing = false;

  // Cache dla Gemini odpowiedzi
  private geminiCache: Map<string, string | null> = new Map();
  private errorCount: number = 0;
  private maxTotalErrorRetries: number = 5;
  private userAgents: string[] = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
  ];

  constructor(settings?: Partial<ScraperSettings>) {
    this.settings = {
      headless: true,
      slowMo: 0,
      userAgentRotation: true,
      proxyEnabled: false,
      ...settings,
    };
  }

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  // Funkcja do sprawdzania czy województwo jest dozwolone
  private isAllowedRegion(location: string | undefined): { allowed: boolean; region: string | null } {
    if (!location) return { allowed: false, region: null };
    
    // Próbujemy wyciągnąć województwo - format "Miasto (Województwo)"
    const regionMatch = location.match(/\(([^)]+)\)$/);
    const region = regionMatch ? regionMatch[1].trim() : null;
    
    // Jeśli nie znaleziono województwa, zakładamy że nie jest dozwolone
    if (!region) return { allowed: false, region: null };
    
    // Sprawdź czy województwo jest na liście dozwolonych
    const allowedRegions = this.settings.allowedRegions || [
      'Mazowieckie', 'Łódzkie', 'Lubelskie', 'Świętokrzyskie', 'Podlaskie'
    ];
    
    return { 
      allowed: allowedRegions.includes(region),
      region 
    };
  }

  // Funkcja do wyciągania lokalizacji z ogłoszenia
  private async extractLocationFromAd(page: Page, adId: string): Promise<string | null> {
    try {
      // Używamy dokładnego odpowiednika XPath jako selektor CSS
      const selector = 'body > div:nth-child(1) > div > div > div > main > div > section:nth-of-type(2) > div:nth-child(2) > div:nth-child(3) > div:nth-child(2) > button > p';

      await this.logScraperAction('info', `Szukanie lokalizacji dla ogłoszenia ${adId} używając selektora: "${selector}"`);

      // Sprawdź czy selektor istnieje na stronie
      const exists = await page.$(selector);
      if (!exists) {
        await this.logScraperAction('warn', `Selektor "${selector}" nie znaleziony na stronie dla ogłoszenia ${adId}`);

        // Spróbuj XPath jako backup
        await this.logScraperAction('info', `Próba użycia XPath jako backup dla ogłoszenia ${adId}`);
        try {
          const xpathResult = await page.evaluate(() => {
            const xpath = '/html/body/div[1]/div/div/div/main/div/aside/div[2]/ul/a/p';
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            const element = result.singleNodeValue as Element;
            return element ? element.textContent?.trim() : null;
          });

          if (xpathResult && xpathResult.length > 2) {
            await this.logScraperAction('info', `XPath znalazł tekst: "${xpathResult}" dla ogłoszenia ${adId}`);

            // Sprawdź czy to jest już w formacie "Miasto (Województwo)"
            if (xpathResult.includes('(') && xpathResult.includes(')')) {
              await this.logScraperAction('info', `XPath - lokalizacja już w poprawnym formacie: "${xpathResult}"`);

              // Sprawdź czy województwo jest dopuszczalne
              const locationCheck = this.isAllowedRegion(xpathResult);
              if (locationCheck.allowed) {
                await this.logScraperAction('info', `XPath - Województwo "${locationCheck.region}" jest dopuszczalne`);
                return xpathResult;
              } else {
                await this.logScraperAction('warn', `XPath - Województwo "${locationCheck.region}" nie jest dopuszczalne - ogłoszenie zostanie pominięte`);
                return null; // Zwróć null, aby oznaczyć że lokalizacja nie jest dopuszczalna
              }
            }

            // Użyj Gemini do rozpoznania województwa
            await this.logScraperAction('info', `XPath - używam Gemini do rozpoznania województwa dla: "${xpathResult}"`);
            const region = await this.getRegionFromGemini(xpathResult);

            if (region) {
              const city = xpathResult.split(',')[0].trim();
              const formattedLocation = `${city} (${region})`;
              await this.logScraperAction('info', `XPath + Gemini zmapował lokalizację: "${xpathResult}" -> "${formattedLocation}"`);

              // Sprawdź czy województwo jest dopuszczalne
              const locationCheck = this.isAllowedRegion(formattedLocation);
              if (locationCheck.allowed) {
                await this.logScraperAction('info', `XPath - Województwo "${region}" jest dopuszczalne`);
                return formattedLocation;
              } else {
                await this.logScraperAction('warn', `XPath - Województwo "${region}" nie jest dopuszczalne - ogłoszenie zostanie pominięte`);
                return null; // Zwróć null, aby oznaczyć że lokalizacja nie jest dopuszczalna
              }
            } else {
              await this.logScraperAction('warn', `XPath - Gemini nie rozpoznał województwa dla: "${xpathResult}"`);
              return xpathResult;
            }
          }
        } catch (xpathError) {
          await this.logScraperAction('warn', `Błąd podczas używania XPath: ${xpathError instanceof Error ? xpathError.message : String(xpathError)}`);
        }

        return null;
      }

      // Pobierz tekst ze wszystkich pasujących elementów
      const locationsTexts = await page.$$eval(selector, elements =>
        elements.map(el => el.textContent ? el.textContent.trim() : null)
      );

      await this.logScraperAction('info', `Selektor "${selector}" zwrócił ${locationsTexts.length} elementów: ${JSON.stringify(locationsTexts)}`);

      // Znajdź pierwszy niepusty tekst
      for (const text of locationsTexts) {
        if (text && text.length > 2) { // Ignoruj bardzo krótkie teksty
          await this.logScraperAction('info', `Znaleziono tekst lokalizacji: "${text}" dla ogłoszenia ${adId}`);

          // Sprawdź czy to jest już w formacie "Miasto (Województwo)"
          if (text.includes('(') && text.includes(')')) {
            await this.logScraperAction('info', `Lokalizacja już w poprawnym formacie: "${text}"`);

            // Sprawdź czy województwo jest dopuszczalne
            const locationCheck = this.isAllowedRegion(text);
            if (locationCheck.allowed) {
              await this.logScraperAction('info', `Województwo "${locationCheck.region}" jest dopuszczalne`);
              return text;
            } else {
              await this.logScraperAction('warn', `Województwo "${locationCheck.region}" nie jest dopuszczalne - ogłoszenie zostanie pominięte`);
              return null; // Zwróć null, aby oznaczyć że lokalizacja nie jest dopuszczalna
            }
          }

          // Użyj Gemini do rozpoznania województwa
          await this.logScraperAction('info', `Używam Gemini do rozpoznania województwa dla: "${text}"`);
          const region = await this.getRegionFromGemini(text);

          if (region) {
            // Wyciągnij miasto z tekstu (pierwszy element przed przecinkiem lub cały tekst)
            const city = text.split(',')[0].trim();
            const formattedLocation = `${city} (${region})`;
            await this.logScraperAction('info', `Gemini zmapował lokalizację: "${text}" -> "${formattedLocation}"`);

            // Sprawdź czy województwo jest dopuszczalne
            const locationCheck = this.isAllowedRegion(formattedLocation);
            if (locationCheck.allowed) {
              await this.logScraperAction('info', `Województwo "${region}" jest dopuszczalne`);
              return formattedLocation;
            } else {
              await this.logScraperAction('warn', `Województwo "${region}" nie jest dopuszczalne - ogłoszenie zostanie pominięte`);
              return null; // Zwróć null, aby oznaczyć że lokalizacja nie jest dopuszczalna
            }
          } else {
            await this.logScraperAction('warn', `Gemini nie rozpoznał województwa dla: "${text}"`);
            // Zwróć oryginalny tekst jako fallback
            return text;
          }
        }
      }

      // Jeśli nie znaleziono lokalizacji, zwróć null
      await this.logScraperAction('warn', `Nie znaleziono lokalizacji dla ogłoszenia ${adId}`);
      return null;
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas wyciągania lokalizacji dla ogłoszenia ${adId}: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }
  
  async initialize(): Promise<void> {
    try {
      const options: Parameters<typeof puppeteer.launch>[0] = {
        headless: this.settings.headless,
        slowMo: this.settings.slowMo,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
          '--window-size=1920,1080',
          '--disable-images',
        ],
      };

      if (this.settings.proxyEnabled && this.settings.proxyUrl && options.args) {
        options.args.push(`--proxy-server=${this.settings.proxyUrl}`);
      }

      this.browser = await puppeteer.launch(options);
      
      // Ustaw referencję do przeglądarki w global.cronFunctions, aby można było ją zamknąć w razie potrzeby
      if (global.cronFunctions && typeof global.cronFunctions.setScraperBrowser === 'function') {
        global.cronFunctions.setScraperBrowser(this.browser);
      }
      
      this.page = await this.browser.newPage();

      // Ustaw User-Agent
      if (this.settings.userAgentRotation) {
        await this.page.setUserAgent(this.getRandomUserAgent());
      }

      // Ustaw viewport
      await this.page.setViewport({ width: 1920, height: 1080 });

      // Ustaw timeout
      this.page.setDefaultNavigationTimeout(60000);
    } catch (error) {
      console.error('Błąd podczas inicjalizacji scrapera:', error);
      throw error;
    }
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.page = null;
    }
  }

  async acceptCookies(): Promise<void> {
    if (!this.page) throw new Error('Scraper nie został zainicjalizowany');

    try {
      await this.page.waitForSelector('#__next');

      // Pobierz selektor przycisku akceptacji cookies z bazy danych
      const cookieSelector = await prisma.selector.findFirst({
        where: { name: 'cookieConsent', isActive: true }
      });

      if (cookieSelector) {
        // Używamy pola cssSelector
        const cookieSelectorValue = cookieSelector.cssSelector;
        const cookieButton = await this.page.$(cookieSelectorValue);
        if (cookieButton) {
          await cookieButton.click();
          await this.logScraperAction('info', 'Zaakceptowano cookies');
          
          // Zwiększony czas oczekiwania po kliknięciu przycisku (z 2000ms na 4000ms)
          await this.logScraperAction('info', 'Oczekiwanie 4s po akceptacji cookies');
          await new Promise(resolve => setTimeout(resolve, 4000));
        }
      }
    } catch (error) {
      console.warn('Nie znaleziono przycisku akceptacji cookies:', error);
      await this.logScraperAction('warn', `Nie znaleziono przycisku akceptacji cookies: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async checkNumberOfPages(): Promise<number> {
    if (!this.page) throw new Error('Scraper nie został zainicjalizowany');

    try {
      // Pobierz selektor przycisku następnej strony z bazy danych
      const nextPageSelector = await prisma.selector.findFirst({
        where: { name: 'nextPage', isActive: true }
      });

      if (!nextPageSelector) return 1;

      await this.logScraperAction('info', 'Sprawdzanie liczby stron...');
      
      // Przewiń stronę na dół, aby elementy paginacji się załadowały
      await this.logScraperAction('info', 'Przewijanie strony na dół, aby załadować elementy paginacji');
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      // Zwiększony czas oczekiwania po przewinięciu (z 1000ms na 3000ms)
      await this.logScraperAction('info', 'Oczekiwanie 3s na załadowanie elementów paginacji');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Dodatkowe przewinięcie aby upewnić się, że wszystkie elementy się załadowały
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
        return document.body.scrollHeight;
      });
      
      // Dodatkowy krótki czas oczekiwania po drugim przewinięciu
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Strategia: Użyj XPath do znalezienia elementu paginacji
      const pagesCount = await this.page.evaluate(() => {
        const xpath = '/html/body/div[1]/div/div/div/div[2]/main/div[2]/div/div[3]/div[4]/ul/li[7]';
        const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        const xpathElement = result.singleNodeValue as Element;

        if (xpathElement && xpathElement.textContent) {
          const pageNumber = parseInt(xpathElement.textContent.trim());
          if (!isNaN(pageNumber)) {
            console.log(`Znaleziono liczbę stron (XPath): ${pageNumber}`);
            return pageNumber;
          }
        }

        return null;
      });

      if (pagesCount) {
        await this.logScraperAction('info', `Znaleziono liczbę stron: ${pagesCount}`);
        return pagesCount;
      }

      // Jeśli XPath nie zadziałał, użyj fallback na 200 stron
      await this.logScraperAction('warn', 'XPath nie znalazł liczby stron - używam fallback 200 stron');
      return 200;
    } catch (error) {
      console.error('Błąd podczas sprawdzania liczby stron:', error);
      await this.logScraperAction('error', `Błąd podczas sprawdzania liczby stron: ${error instanceof Error ? error.message : String(error)}`);
      return 1;
    }
  }

  validateMobilePhone(phoneNumber: string, region = 'PL'): boolean {
    if (!phoneNumber) return false;

    try {
      const parsedPhone = phoneUtil.parse(phoneNumber, region);
      return (
        phoneUtil.isValidNumberForRegion(parsedPhone, region) &&
        phoneUtil.getNumberType(parsedPhone) === libphonenumber.PhoneNumberType.MOBILE
      );
    } catch {
      return false;
    }
  }

  // Funkcja do przetwarzania kolejki Gemini z rate limiting
  private async processGeminiQueue(): Promise<void> {
    if (this.geminiProcessing || this.geminiRequestQueue.length === 0) {
      return;
    }

    this.geminiProcessing = true;

    while (this.geminiRequestQueue.length > 0) {
      const currentMinute = Math.floor(Date.now() / 60000);

      // Reset licznika co minutę
      if (currentMinute !== this.geminiLastMinute) {
        this.geminiRequestCount = 0;
        this.geminiLastMinute = currentMinute;
      }

      // Sprawdź limit (14 żądań na minutę, zostawiamy 1 jako bufor)
      if (this.geminiRequestCount >= 14) {
        await this.logScraperAction('info', `Osiągnięto limit Gemini API (14/15 żądań na minutę). Oczekiwanie...`);
        // Czekaj do następnej minuty
        const waitTime = (currentMinute + 1) * 60000 - Date.now();
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }

      const request = this.geminiRequestQueue.shift();
      if (!request) break;

      try {
        this.geminiRequestCount++;
        await this.logScraperAction('info', `Gemini API żądanie ${this.geminiRequestCount}/14 w tej minucie dla: "${request.locationText}"`);

        const result = await this.callGeminiAPI(request.locationText);
        request.resolve(result);

        // Dodaj małe opóźnienie między żądaniami (4 sekundy = 15 żądań na minutę)
        await new Promise(resolve => setTimeout(resolve, 4100));

      } catch (error) {
        request.reject(error instanceof Error ? error : new Error(String(error)));
      }
    }

    this.geminiProcessing = false;
  }

  // Funkcja do bezpośredniego wywołania Gemini API
  private async callGeminiAPI(locationText: string): Promise<string | null> {
    try {
      const apiKey = 'AIzaSyANM77YO9DT5yKFZb6ChKcZJQWURLx-25k';
      const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

      const prompt = `${locationText}\nJakie to województwo? Odpowiedz jednym słowem, np. podlaskie, mazowieckie, łódzkie, itd.`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ]
        })
      });

      if (!response.ok) {
        await this.logScraperAction('error', `Błąd API Gemini: ${response.status} ${response.statusText}`);
        return null;
      }

      const data = await response.json();
      const geminiResponse = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase();

      if (geminiResponse) {
        await this.logScraperAction('info', `Gemini rozpoznał województwo dla "${locationText}": "${geminiResponse}"`);
        return geminiResponse;
      }

      return null;
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas wywołania Gemini API: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  // Funkcja do rozpoznawania województwa za pomocą Gemini 2.0 Flash z rate limiting
  private async getRegionFromGemini(locationText: string): Promise<string | null> {
    // Sprawdź cache
    const normalizedText = locationText.toLowerCase().trim();
    if (this.geminiCache.has(normalizedText)) {
      const cachedResult = this.geminiCache.get(normalizedText) || null;
      await this.logScraperAction('info', `Gemini cache hit dla "${locationText}": "${cachedResult}"`);
      return cachedResult;
    }

    return new Promise((resolve, reject) => {
      // Dodaj żądanie do kolejki
      this.geminiRequestQueue.push({
        locationText,
        resolve: (result) => {
          // Zapisz w cache
          this.geminiCache.set(normalizedText, result);
          resolve(result);
        },
        reject
      });

      this.logScraperAction('info', `Dodano do kolejki Gemini: "${locationText}" (kolejka: ${this.geminiRequestQueue.length} żądań)`);

      // Rozpocznij przetwarzanie kolejki
      this.processGeminiQueue().catch(reject);
    });
  }

  async scrapeListingPage(url: string): Promise<AdData[]> {
    if (!this.page) throw new Error('Scraper nie został zainicjalizowany');

    try {
      // Przejdź do strony z mechanizmem retry
      await this.logScraperAction('info', `Przechodzenie do strony: ${url}`);
      await this.withRetry(
        async () => {
          await this.page!.goto(url, { 
            waitUntil: 'networkidle2',
            timeout: 30000 
          });
        },
        3,
        2000,
        'Nawigacja do strony z ogłoszeniami'
      );

      // Akceptuj cookies
      await this.acceptCookies();

      // Pobierz selektory z bazy danych tylko w celu znalezienia kontenera ogłoszeń
      await this.logScraperAction('info', 'Pobieranie selektorów z bazy danych');
      const adContainerSelector = await prisma.selector.findFirst({
        where: { name: 'adContainer', isActive: true }
      });
      
      const containerSelector = adContainerSelector?.cssSelector || 
                                '[data-testid="search-results"] > div > article';
      
      // Dodaj czas na załadowanie ogłoszeń
      await this.logScraperAction('info', 'Oczekiwanie na załadowanie ogłoszeń...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Pobierz tylko ID ogłoszeń z listy - to jedyne, co potrzebujemy do API
      await this.logScraperAction('info', `Pobieranie ID ogłoszeń ze strony używając selektora: ${containerSelector}`);

      // Najpierw sprawdź, czy strona się załadowała i czy są jakiekolwiek ogłoszenia
      const pageInfo = await this.page.evaluate(() => {
        const title = document.title;
        const url = window.location.href;
        const bodyText = document.body ? document.body.innerText.substring(0, 200) : 'BRAK BODY';

        // Sprawdź różne możliwe selektory ogłoszeń
        const selectors = [
          '[data-testid="search-results"] > div > article',
          'article[data-id]',
          '[data-testid="search-results"] article',
          'div[data-id]',
          '.offer-item',
          '.offer'
        ];

        const selectorResults: Record<string, number> = {};
        selectors.forEach(sel => {
          selectorResults[sel] = document.querySelectorAll(sel).length;
        });

        return { title, url, bodyText, selectorResults };
      });

      await this.logScraperAction('info', `Strona: ${pageInfo.title}`);
      await this.logScraperAction('info', `URL: ${pageInfo.url}`);
      await this.logScraperAction('info', `Początek treści: ${pageInfo.bodyText}`);
      await this.logScraperAction('info', `Wyniki selektorów: ${JSON.stringify(pageInfo.selectorResults)}`);

      const adIds = await this.page.evaluate((selector) => {
        const ids: string[] = [];

        const adElements = document.querySelectorAll(selector);
        console.log(`Znaleziono ${adElements.length} elementów ogłoszeń dla selektora: ${selector}`);

        if (adElements.length === 0) {
          // Spróbuj alternatywnych selektorów
          const alternativeSelectors = [
            'article[data-id]',
            '[data-testid="search-results"] article',
            'div[data-id]',
            '.offer-item',
            '.offer'
          ];

          for (const altSelector of alternativeSelectors) {
            const altElements = document.querySelectorAll(altSelector);
            console.log(`Alternatywny selektor ${altSelector}: ${altElements.length} elementów`);

            if (altElements.length > 0) {
              altElements.forEach((element) => {
                const externalId = element.getAttribute('data-id');
                if (externalId && externalId !== 'BRAK') {
                  console.log(`Znaleziono ID ogłoszenia (alt): ${externalId}`);
                  ids.push(externalId);
                }
              });
              break; // Użyj pierwszego działającego selektora
            }
          }
        } else {
          adElements.forEach((element) => {
            const externalId = element.getAttribute('data-id');
            if (externalId && externalId !== 'BRAK') {
              console.log(`Znaleziono ID ogłoszenia: ${externalId}`);
              ids.push(externalId);
            } else {
              console.log('Pominięto ogłoszenie bez ID');
            }
          });
        }

        return ids;
      }, containerSelector);

      await this.logScraperAction('info', `Znaleziono ${adIds.length} ID ogłoszeń na stronie`);

      // Tablica z danymi ogłoszeń
      const ads: AdData[] = [];
      
      // Pobierz szczegóły każdego ogłoszenia z API + numery telefonów
      for (let i = 0; i < adIds.length; i++) {
      // Dodaj opóźnienie między każdym ogłoszeniem (w trybie turbo znacznie mniejsze)
      if (i > 0) {
        const delayTime = this.settings.turboMode ? 100 : 1000;
        await this.logScraperAction('info', `Oczekiwanie ${delayTime}ms między ogłoszeniami...`);
        await new Promise(resolve => setTimeout(resolve, delayTime));
      }
        
        const externalId = adIds[i];
        await this.logScraperAction('info', `Pobieranie danych dla ogłoszenia ${i+1}/${adIds.length}: ${externalId}`);
        
        // 1. Pobierz dane z API
        const apiData = await this.fetchAdDetailsFromApi(externalId);
        
        // 2. Zamapuj dane API na strukturę AdData
        const ad = this.mapApiDataToAdData(apiData, externalId);
        
        // Sprawdź czy ogłoszenie nie zostało oznaczone do pominięcia z powodu województwa
        if (ad.brandModel === 'Auto (pominięto - złe województwo)') {
          await this.logScraperAction('info', `Pomijanie ogłoszenia ${externalId} z lokalizacją ${ad.location}`);
          continue; // Przejdź do następnego ogłoszenia
        }
        
        // 3. Pobierz numery telefonów
        try {
          // Otwórz nową kartę dla zapytania o numery telefonów
          const page = await this.browser!.newPage();
          try {
            // Ustaw viewport i timeouty
            await page.setViewport({ width: 1366, height: 768 });
            page.setDefaultNavigationTimeout(30000);
            
            // Pobierz numery telefonów
            const phonesUrl = `https://www.otomoto.pl/ajax/misc/contact/all_phones/${externalId}`;
            await this.logScraperAction('info', `Pobieranie numerów telefonów z: ${phonesUrl}`);
            
            const phonesResponse = await page.goto(phonesUrl, { waitUntil: 'networkidle2', timeout: 15000 });
            
            if (!phonesResponse) {
              await this.logScraperAction('error', `Nie otrzymano odpowiedzi dla numerów telefonów: ${phonesUrl}`);
            } else {
              const phonesStatus = phonesResponse.status();
              if (phonesStatus !== 200) {
                await this.logScraperAction('error', `Błąd HTTP ${phonesStatus} przy pobieraniu numerów: ${phonesUrl}`);
              } else {
                const phonesText = await phonesResponse.text();
                
                if (phonesText) {
                  try {
                    const phonesData = JSON.parse(phonesText);
                    if (Array.isArray(phonesData)) {
                      ad.phones = phonesData.map((phone: { number: string }) => phone.number);
                      await this.logScraperAction('info', `Pobrano ${ad.phones.length} numerów telefonów dla ogłoszenia ${externalId}`);

                      // 4. Zapisz ogłoszenie do bazy
                      await this.saveAdToDatabase(ad);

                      // 5. Dodaj do listy ogłoszeń
                      ads.push(ad);
                    } else {
                      await this.logScraperAction('warn', `Nieoczekiwany format danych telefonów: ${typeof phonesData}`);
                    }
                  } catch (e) {
                    await this.logScraperAction('error', `Błąd parsowania numerów telefonów dla ogłoszenia ${externalId}: ${e instanceof Error ? e.message : String(e)}`);
                  }
                } else {
                  await this.logScraperAction('warn', `Pusta odpowiedź dla numerów telefonów ogłoszenia ${externalId}`);
                }
              }
            }
          } catch (pageError) {
            await this.logScraperAction('error', `Błąd podczas przetwarzania numerów telefonów ogłoszenia ${externalId}: ${pageError instanceof Error ? pageError.message : String(pageError)}`);
            // Nawet jeśli nie udało się pobrać numerów telefonów, możemy dodać ogłoszenie do listy
            ads.push(ad);
          } finally {
            // Zawsze zamykaj kartę, aby zwolnić zasoby
            await page.close();
          }
        } catch (error) {
          await this.logScraperAction('error', `Błąd podczas tworzenia karty dla numerów telefonów ogłoszenia ${externalId}: ${error instanceof Error ? error.message : String(error)}`);
          // Nawet jeśli nie udało się pobrać numerów telefonów, możemy dodać ogłoszenie do listy
          ads.push(ad);
        }
      }

      await this.logScraperAction('info', `Pomyślnie pobrano dane dla ${ads.length} ogłoszeń z API i numerów telefonów`);
      return ads;
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas scrapowania strony: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  // Nowa metoda do zapisywania pojedynczego ogłoszenia
  // Nowa metoda do pobierania szczegółów ogłoszenia z API
  async fetchAdDetailsFromApi(externalId: string): Promise<Record<string, unknown> | null> {
    if (!this.page) throw new Error('Scraper nie został zainicjalizowany');
    if (!externalId || externalId === 'BRAK') {
      await this.logScraperAction('warn', 'Próba pobrania danych API dla nieprawidłowego ID');
      return null;
    }

    try {
      // Otwórz nową kartę dla zapytania API, aby nie tracić stanu głównej strony
      const page = await this.browser!.newPage();
      try {
        // Ustaw User-Agent i timeouty
        if (this.settings.userAgentRotation) {
          await page.setUserAgent(this.getRandomUserAgent());
        }
        page.setDefaultNavigationTimeout(20000);
        
        // Utwórz URL do strony ogłoszenia - potrzebujemy ją, aby pobrać lokalizację
        const adUrl = `https://www.otomoto.pl/osobowe/${externalId}`;
        await this.logScraperAction('info', `Pobieranie danych ogłoszenia z: ${adUrl}`);
        
        // Najpierw przejdź do strony ogłoszenia, aby wyciągnąć lokalizację
        const adPageResponse = await page.goto(adUrl, { waitUntil: 'domcontentloaded', timeout: 20000 });
        
        if (!adPageResponse) {
          await this.logScraperAction('error', `Nie otrzymano odpowiedzi dla strony ogłoszenia: ${adUrl}`);
        } else {
          // Poczekaj na załadowanie treści ogłoszenia
          await page.waitForSelector('body', { timeout: 10000 }).catch(() => {
            this.logScraperAction('warn', 'Timeout oczekiwania na selektor body - kontynuowanie');
          });
          
          // Wyciągnij lokalizację używając selektorów
          const location = await this.extractLocationFromAd(page, externalId);
          if (location) {
            // Sprawdź czy województwo jest na liście dozwolonych
            const { allowed, region } = this.isAllowedRegion(location);
            if (!allowed) {
              // Jeżeli województwo nie jest dozwolone, ignoruj to ogłoszenie
              await this.logScraperAction('info', `Zignorowałem - złe województwo: ${location} (region: ${region || 'nie wykryto'})`);
              return { ignoredByRegion: true, location };
            } else {
              await this.logScraperAction('info', `Dozwolone województwo: ${region}, lokalizacja: ${location}`);
            }
          } else {
            await this.logScraperAction('warn', `Nie znaleziono lokalizacji dla ogłoszenia ${externalId}`);
          }
        }
        
        // Utwórz URL API - użyjemy bezpośredniego URL dla szczegółów ogłoszenia
        const apiUrl = `https://www.otomoto.pl/api/v1/ad/${externalId}`;

        await this.logScraperAction('info', `Pobieranie danych API z: ${apiUrl}`);
        
        // Wykonaj zapytanie do API
        const apiResponse = await page.goto(apiUrl, { waitUntil: 'networkidle2', timeout: 15000 });
        
        if (!apiResponse) {
          await this.logScraperAction('error', `Nie otrzymano odpowiedzi API dla ogłoszenia: ${apiUrl}`);
          return null;
        }
        
        const apiStatus = apiResponse.status();
        if (apiStatus !== 200) {
          await this.logScraperAction('error', `Błąd HTTP ${apiStatus} przy pobieraniu danych API: ${apiUrl}`);
          return null;
        }
        
        const apiText = await apiResponse.text();
        if (!apiText) {
          await this.logScraperAction('warn', `Pusta odpowiedź API dla ogłoszenia ${externalId}`);
          return null;
        }
        
        try {
          const apiData = JSON.parse(apiText);
          
          // Dodaj szczegółowe debugowanie odpowiedzi API 
          // Zapisz pierwszych 200 znaków odpowiedzi, aby nie zaśmiecać logów
          const shortResponse = JSON.stringify(apiData).substring(0, 200) + '...';
          await this.logScraperAction('info', `Odpowiedź API dla ${externalId}: ${shortResponse}`);
          
          // Sprawdź, czy dane mają oczekiwaną strukturę
          if (!apiData[externalId] && Object.keys(apiData).length > 0) {
            await this.logScraperAction('info', `Dane API nie są zagnieżdżone pod ID ogłoszenia, ale mają klucze: ${Object.keys(apiData).join(', ')}`);
          }
          
          return apiData;
        } catch (e) {
          await this.logScraperAction('error', `Błąd parsowania odpowiedzi API dla ogłoszenia ${externalId}: ${e instanceof Error ? e.message : String(e)}`);
          return null;
        }
      } catch (pageError) {
        await this.logScraperAction('error', `Błąd podczas pobierania danych API dla ogłoszenia ${externalId}: ${pageError instanceof Error ? pageError.message : String(pageError)}`);
        return null;
      } finally {
        // Zawsze zamykaj kartę, aby zwolnić zasoby
        await page.close();
      }
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas tworzenia karty dla API ogłoszenia ${externalId}: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  // Metoda do mapowania danych API na strukturę AdData
  mapApiDataToAdData(apiData: Record<string, unknown> | null, externalId: string): AdData {
    // Jeśli ogłoszenie zostało oznaczone do pominięcia z powodu niewłaściwego województwa
    if (apiData && apiData.ignoredByRegion) {
      this.logScraperAction('info', `Mapowanie ogłoszenia ${externalId} - pominięte z powodu niewłaściwego województwa (${apiData.location || 'brak lokalizacji'})`);
      return {
        externalId: externalId,
        brandModel: 'Auto (pominięto - złe województwo)',
        location: typeof apiData.location === 'string' ? apiData.location : undefined,
        phones: [],
        apiData: null
      };
    }
    
    if (!apiData) {
      return {
        externalId: externalId,
        brandModel: 'Auto (brak danych API)',
        phones: [],
        apiData: null
      };
    }
    
    try {
      // Pobierz dane z pierwszego klucza w API (ID ogłoszenia) lub bezpośrednio z obiektu
      const adData = (apiData[externalId] || apiData[Object.keys(apiData)[0]] || apiData) as Record<string, unknown>;

      if (!adData) {
        this.logScraperAction('warn', `Nieprawidłowa struktura API dla ogłoszenia ${externalId}`);
        return {
          externalId: externalId,
          brandModel: 'Auto (nieprawidłowa struktura API)',
          phones: [],
          apiData: apiData
        };
      }

      // Podstawowe dane ogłoszenia
      const ad: AdData = {
        externalId: externalId,
        brandModel: (typeof adData.title_full === 'string' ? adData.title_full : null) || 'Auto',
        desc: '',
        url: typeof adData.url === 'string' ? adData.url : undefined,
        location: typeof apiData.location === 'string' ? apiData.location : undefined, // Dodajemy lokalizację pobraną ze strony ogłoszenia
        phones: [],
        apiData: apiData // Zachowujemy pełne dane API do ewentualnego użycia
      };
      
      // Parametry z obiektu params
      const params = adData.params as Record<string, unknown> | undefined;
      if (params) {
        // Rok produkcji
        const year = params.year as Record<string, unknown> | undefined;
        if (year) {
          const yearValue = typeof year.valueHuman_pl === 'string' ? year.valueHuman_pl.trim() :
                           typeof year.value === 'string' ? year.value : undefined;
          if (yearValue) ad.year = yearValue;
        }

        // Przebieg
        const mileage = params.mileage as Record<string, unknown> | undefined;
        if (mileage) {
          const mileageValue = typeof mileage.valueHuman_pl === 'string' ? mileage.valueHuman_pl :
                              typeof mileage.value === 'number' ? `${mileage.value} km` : undefined;
          if (mileageValue) ad.mileage = mileageValue;
        }

        // Cena
        const price = params.price as Record<string, unknown> | undefined;
        if (price) {
          if (typeof price.valueHuman_pl === 'string') {
            ad.price = price.valueHuman_pl;
          } else if (Array.isArray(price.value) && price.value.length > 1) {
            ad.price = `${price.value[1]} ${price.currency || ''}`;
          } else if (typeof price.price_raw_human === 'string' && typeof price.price_currency === 'string') {
            ad.price = `${price.price_raw_human} ${price.price_currency}`;
          }
        }
      }

      // Dodatkowe pola z sekcji category_name_short_list
      if (typeof adData.category_name_short_list === 'string') {
        ad.desc = ad.desc || adData.category_name_short_list;
      }
      
      return ad;
    } catch (e) {
      this.logScraperAction('error', `Błąd podczas mapowania danych API dla ogłoszenia ${externalId}: ${e instanceof Error ? e.message : String(e)}`);
      return {
        externalId: externalId,
        brandModel: 'Auto (błąd mapowania)',
        phones: [],
        apiData: apiData
      };
    }
  }

  async saveAdToDatabase(ad: AdData): Promise<boolean> {
    try {
      // Sprawdź czy ogłoszenie ma wszystkie wymagane pola
      if (!ad.externalId || !ad.brandModel) {
        await this.logScraperAction('warn', `Pominięto ogłoszenie z brakującymi danymi: externalId=${ad.externalId}, brandModel=${ad.brandModel}`);
        return false;
      }
      
      // Nie zapisuj ogłoszeń z brakiem danych API lub ogłoszeń pominiętych z powodu województwa
      if (ad.brandModel === 'Auto (brak danych API)' || 
          ad.brandModel === 'Auto (nieprawidłowa struktura API)' || 
          ad.brandModel === 'Auto (błąd mapowania)' ||
          ad.brandModel === 'Auto (pominięto - złe województwo)') {
        await this.logScraperAction('warn', `Pominięto ogłoszenie z brandModel=${ad.brandModel}`);
        return false;
      }
      
      // Debuguj dane ogłoszenia przed zapisem
      await this.logScraperAction('info', `Zapisywanie ogłoszenia: ${ad.externalId} - ${ad.brandModel}`);
      await this.logScraperAction('info', `Dane: ${JSON.stringify({
        externalId: ad.externalId,
        brandModel: ad.brandModel,
        phones: ad.phones
      })}`);
    
      // Użyj transakcji do zapisania ogłoszenia i powiązanych danych
      await prisma.$transaction(async (tx) => {
        // Sprawdź, czy ogłoszenie już istnieje
        const existingAd = await tx.ad.findUnique({
          where: { externalId: ad.externalId },
        });

        if (!existingAd) {
          // Utwórz nowe ogłoszenie
          const newAd = await tx.ad.create({
            data: {
              externalId: ad.externalId,
              brandModel: ad.brandModel || 'Auto',
              desc: ad.desc,
              date: ad.date,
              location: ad.location,
              mileage: ad.mileage,
              year: ad.year,
              price: ad.price,
              url: ad.url,
            },
          });

          await this.logScraperAction('info', `Zapisano ogłoszenie w bazie: ${ad.brandModel} (ID: ${ad.externalId}, DB_ID: ${newAd.id})`);

          // Dodaj numery telefonów do bazy i znajdź pierwszy prawidłowy numer mobilny
          let foundValidMobilePhone = false;
          
          if (ad.phones && ad.phones.length > 0) {
            await this.logScraperAction('info', `Zapisywanie ${ad.phones.length} numerów telefonów dla ogłoszenia ${ad.externalId}`);
            
            for (const phoneNumber of ad.phones) {
              if (!phoneNumber) {
                await this.logScraperAction('warn', `Pominięto pusty numer telefonu dla ogłoszenia ${ad.externalId}`);
                continue;
              }
              
              // Utwórz telefon
              const phone = await tx.phone.create({
                data: {
                  number: phoneNumber,
                  adId: newAd.id,
                },
              });
              
              await this.logScraperAction('info', `Zapisano telefon: ${phoneNumber} dla ogłoszenia ${ad.externalId} (DB_ID: ${phone.id})`);
              
              // Sprawdź, czy to prawidłowy numer telefonu komórkowego i czy jeszcze nie znaleźliśmy prawidłowego numeru
              if (!foundValidMobilePhone && this.validateMobilePhone(phoneNumber)) {
                // Sprawdź, czy numer nie jest już zablokowany
                const isBlocked = await tx.blockedPhone.findFirst({
                  where: { number: phoneNumber }
                });
                
                // Sprawdź, czy numer nie jest już w kolejce do wysyłki
                const isAlreadyInQueue = await tx.phoneToSend.findFirst({
                  where: { number: phoneNumber }
                });
                
                // Sprawdź, czy numer nie otrzymał SMS-a w określonym czasie
                const settings = await tx.settings.findFirst();
                const smsResendDays = settings?.smsResendDays || 90; // Domyślnie 90 dni
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - smsResendDays);
                
                const recentSms = await tx.sentSmsHistory.findFirst({
                  where: {
                    number: phoneNumber,
                    sentAt: {
                      gte: cutoffDate
                    }
                  }
                });
                
                // Jeśli numer nie jest zablokowany, nie jest w kolejce i nie otrzymał SMS-a w określonym czasie
                if (!isBlocked && !isAlreadyInQueue && !recentSms) {
                  const phoneToSend = await tx.phoneToSend.create({
                    data: { number: phoneNumber }
                  });
                  await this.logScraperAction('info', `Dodano telefon do kolejki wysyłki: ${phoneNumber} (DB_ID: ${phoneToSend.id})`);
                  foundValidMobilePhone = true;
                } else {
                  if (isBlocked) {
                    await this.logScraperAction('info', `Telefon ${phoneNumber} jest zablokowany, nie dodano do kolejki`);
                  }
                  if (isAlreadyInQueue) {
                    await this.logScraperAction('info', `Telefon ${phoneNumber} jest już w kolejce`);
                  }
                  if (recentSms) {
                    await this.logScraperAction('info', `Telefon ${phoneNumber} otrzymał już SMS w ciągu ostatnich ${smsResendDays} dni (${recentSms.sentAt.toLocaleDateString()}), nie dodano do kolejki`);
                  }
                }
              } else if (!this.validateMobilePhone(phoneNumber)) {
                await this.logScraperAction('info', `Telefon ${phoneNumber} nie jest prawidłowym numerem komórkowym`);
              }
            }
          } else {
            await this.logScraperAction('warn', `Brak numerów telefonów dla ogłoszenia ${ad.externalId}`);
          }

          // Aktualizuj statystyki
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          const stats = await tx.stats.findFirst({
            where: {
              date: {
                gte: today,
              },
            },
          });

          if (stats) {
            await tx.stats.update({
              where: { id: stats.id },
              data: { adsScraped: stats.adsScraped + 1 },
            });
            await this.logScraperAction('info', `Zaktualizowano statystyki dla dzisiaj (ID: ${stats.id})`);
          } else {
            const newStats = await tx.stats.create({
              data: { adsScraped: 1 },
            });
            await this.logScraperAction('info', `Utworzono nowe statystyki dla dzisiaj (ID: ${newStats.id})`);
          }
          
          return true;
        } else {
          await this.logScraperAction('info', `Ogłoszenie już istnieje w bazie: ${ad.brandModel} (ID: ${ad.externalId}, DB_ID: ${existingAd.id})`);
          return false;
        }
      });
      
      return true;
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas zapisywania ogłoszenia ${ad.externalId} do bazy danych: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }
  
  async saveAdsToDatabase(ads: AdData[]): Promise<number> {
    let savedCount = 0;
    
    try {
      // Zapisz log o rozpoczęciu zapisywania
      await this.logScraperAction('info', `Rozpoczęto zapisywanie ${ads.length} ogłoszeń do bazy danych`);
      
      for (const ad of ads) {
        try {
          const isSaved = await this.saveAdToDatabase(ad);
          if (isSaved) {
            savedCount++;
          }
        } catch (adError) {
          await this.logScraperAction('error', `Błąd podczas zapisywania ogłoszenia ${ad.externalId}: ${adError instanceof Error ? adError.message : String(adError)}`);
        }
      }
      
      await this.logScraperAction('info', `Zakończono zapisywanie ogłoszeń do bazy. Zapisano ${savedCount} nowych ogłoszeń.`);
      return savedCount;
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas zapisywania ogłoszeń do bazy danych: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
  
  // Metoda do logowania akcji scrapera
  private async logScraperAction(level: 'info' | 'warn' | 'error', message: string): Promise<void> {
    try {
      // Format logu
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        level,
        message: `[Scraper] ${message}`
      };
      
      // Zapisz log do konsoli
      console[level](`[${timestamp}] [Scraper] ${message}`);
      
      // Ścieżka do pliku logów
      const logDir = path.join(process.cwd(), 'logs');
      const logFilePath = path.join(logDir, 'scraper.log');
      
      // Upewnij się, że katalog istnieje
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      // Zapisz log do pliku
      fs.appendFileSync(
        logFilePath,
        JSON.stringify(logEntry) + os.EOL,
        'utf-8'
      );
    } catch (error) {
      console.error('Błąd podczas logowania:', error);
    }
  }

  async goToNextPage(): Promise<boolean> {
    if (!this.page) throw new Error('Scraper nie został zainicjalizowany');

    try {
      // Zwiększenie timeout dla całej operacji
      this.page.setDefaultNavigationTimeout(90000);
      
      // Poczekaj aż strona w pełni się załaduje
      await this.page.waitForSelector('body', { timeout: 10000 });
      
      // Przewiń stronę na dół, aby elementy paginacji się załadowały
      await this.logScraperAction('info', 'Przewijanie strony na dół, aby załadować elementy paginacji');
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      
      // Poczekaj chwilę, aby elementy paginacji się załadowały
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Pobierz selektor przycisku następnej strony z bazy danych
      const nextPageSelector = await prisma.selector.findFirst({
        where: { name: 'nextPage', isActive: true }
      });

      // Zdefiniuj różne możliwe selektory dla przycisku następnej strony
      const possibleSelectors = [
        // Główny selektor z bazy danych
        nextPageSelector?.cssSelector,
        // Fallback selektory dla otomoto (aktualizowane zgodnie z ich nową strukturą strony)
        '[data-testid="pagination-step-forwards"]',
        'a[title="Next Page"]',
        'a[data-testid="pagination-step-forwards"]',
        'button[data-testid="pagination-step-forwards"]',
        'li[title="Go to next Page"] a',
        'li[title="Go to next Page"]',
        '.pagination a[rel="next"]',
        '.pagination-list .next a',
        '.pagination-next',
        // Ogólny selektor (ostatni link paginacji)
        '.pagination li:last-child a',
        // Tekst "Następna"
        'a:has-text("Następna")',
        'a:has-text("Next")',
        'button:has-text("Następna")',
        'button:has-text("Next")'
      ].filter(Boolean); // Usuń null/undefined z listy

      // Dodaj do logów, jakie selektory będą próbowane
      await this.logScraperAction('info', `Próbowanie selektorów dla przycisku następnej strony: ${possibleSelectors.join(', ')}`);
      
      // Spróbuj każdy selektor po kolei, aż znajdziesz działający
      let nextPageButton = null;
      let usedSelector = '';
      
      for (const selector of possibleSelectors) {
        if (!selector) continue;
        
        await this.logScraperAction('info', `Sprawdzanie selektora: ${selector}`);
        const button = await this.page.$(selector);
        
        if (button) {
          nextPageButton = button;
          usedSelector = selector;
          await this.logScraperAction('info', `Znaleziono przycisk następnej strony używając selektora: ${selector}`);
          break;
        }
      }
      
      if (!nextPageButton) {
        // Dodatkowa weryfikacja - spróbuj znaleźć paginację ogólnie
        const hasPagination = await this.page.$('.pagination, [data-testid="pagination-list"]');
        
        if (hasPagination) {
          // Pobierz tekst z elementu paginacji, aby zobaczyć, czy jesteśmy na ostatniej stronie
          const paginationText = await this.page.evaluate(() => {
            const pagination = document.querySelector('.pagination, [data-testid="pagination-list"]');
            return pagination ? pagination.textContent : '';
          });
          
          await this.logScraperAction('info', `Znaleziono paginację, ale nie przycisk następnej strony. Tekst paginacji: ${paginationText}`);
          await this.logScraperAction('info', `Jesteśmy prawdopodobnie na ostatniej stronie`);
        } else {
          await this.logScraperAction('info', `Nie znaleziono elementu paginacji w ogóle`);
        }
        
        // Alternatywne podejście: próba modyfikacji URL przez zmianę parametru page
        await this.logScraperAction('info', 'Próba alternatywnej metody - zmiana parametru page w URL');
        
        // Pobierz aktualny URL i spróbuj dodać/zmienić parametr page
        const currentUrl = this.page.url();
        let newUrl = '';
        
        // Sprawdź, czy aktualny URL zawiera już parametr page
        if (currentUrl.includes('page=')) {
          // Jeśli tak, zwiększ wartość parametru page o 1
          const url = new URL(currentUrl);
          const currentPage = parseInt(url.searchParams.get('page') || '1');
          const nextPage = currentPage + 1;
          url.searchParams.set('page', nextPage.toString());
          newUrl = url.toString();
          await this.logScraperAction('info', `Zmieniono parametr page z ${currentPage} na ${nextPage}`);
        } else {
          // Jeśli nie, dodaj parametr page=2 na końcu URL
          newUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'page=2';
          await this.logScraperAction('info', `Dodano parametr page=2 do URL`);
        }
        
        await this.logScraperAction('info', `Modyfikacja URL: z ${currentUrl} na ${newUrl}`);
        
        // Przejdź do zmodyfikowanego URL
        await this.page.goto(newUrl, { waitUntil: 'networkidle2' });
        
        // Poczekaj na załadowanie strony
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Sprawdź, czy strona zawiera ogłoszenia (weryfikacja, czy nowy URL działa)
        const adCount = await this.page.$$eval(
          '[data-testid="search-results"] > div > article, .offers article', 
          elements => elements.length
        );
        
        await this.logScraperAction('info', `Liczba znalezionych elementów ogłoszeń po zmianie URL: ${adCount}`);
        
        if (adCount > 0) {
          await this.logScraperAction('info', 'Alternatywna metoda zmiany strony zakończona sukcesem');
          return true;
        }
        
        // Jeśli nie udało się zmienić strony za pomocą alternatywnej metody
        // Zrób zrzut ekranu strony, aby zobaczyć jej stan
        await this.page.screenshot({ path: 'pagination-debug.png' });
        await this.logScraperAction('info', 'Zapisano zrzut ekranu do pagination-debug.png');
        
        // Zapisz źródło HTML strony do analizy
        const html = await this.page.content();
        fs.writeFileSync('pagination-debug.html', html);
        await this.logScraperAction('info', 'Zapisano źródło HTML do pagination-debug.html');
        
        await this.logScraperAction('info', 'Nie udało się przejść do następnej strony żadną metodą - to prawdopodobnie ostatnia strona');
        return false;
      }

      await this.logScraperAction('info', `Kliknięcie przycisku następnej strony (selektor: ${usedSelector})`);
      
      // Poczekaj, aż strona przestanie wykonywać ładowanie zasobów
      await this.page.waitForNetworkIdle({ idleTime: 1000 }).catch(() => {
        // Ignoruj timeout - strona może nigdy nie osiągnąć stanu idle
        this.logScraperAction('warn', 'Timeout oczekiwania na networkIdle - kontynuowanie');
      });
      
      // Kliknij i poczekaj na nawigację
      try {
        // Spróbuj użyć Promise.all do jednoczesnego oczekiwania na kliknięcie i nawigację
        await Promise.all([
          this.page.waitForNavigation({ 
            waitUntil: ['domcontentloaded', 'networkidle2'],
            timeout: 30000 
          }),
          nextPageButton.click({ delay: 100 }) // Dodaj małe opóźnienie przed kliknięciem
        ]);
      } catch (clickError) {
        // Jeśli to nie zadziała, spróbuj kliknąć bez oczekiwania na nawigację
        await this.logScraperAction('warn', `Błąd podczas klikania i oczekiwania na nawigację: ${clickError instanceof Error ? clickError.message : String(clickError)}`);
        await this.logScraperAction('info', 'Próba alternatywnego podejścia - kliknięcie bez oczekiwania na nawigację');
        
        await nextPageButton.click();
        // A następnie poczekaj na zmiany w DOM za pomocą setTimeout
        await new Promise(resolve => setTimeout(resolve, 5000));
      }

      // Poczekaj, aż strona się załaduje - dłuższy czas oczekiwania
      await this.logScraperAction('info', 'Oczekiwanie na załadowanie następnej strony');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Sprawdź, czy adres URL strony się zmienił (dodatkowa weryfikacja)
      const currentUrl = this.page.url();
      await this.logScraperAction('info', `Aktualny URL po przejściu: ${currentUrl}`);
      
      // Dodatkowa weryfikacja zmiany strony - spróbuj znaleźć elementy ogłoszeń
      const adCount = await this.page.$$eval(
        '[data-testid="search-results"] > div > article, .offers article', 
        elements => elements.length
      );
      
      await this.logScraperAction('info', `Liczba znalezionych elementów ogłoszeń na nowej stronie: ${adCount}`);
      
      if (adCount > 0) {
        await this.logScraperAction('info', 'Przejście do następnej strony zakończone sukcesem');
        return true;
      } else {
        await this.logScraperAction('warn', 'Przejście do następnej strony nie powiodło się - nie znaleziono ogłoszeń');
        return false;
      }
    } catch (error) {
      await this.logScraperAction('error', `Błąd podczas przechodzenia do następnej strony: ${error instanceof Error ? error.message : String(error)}`);
      
      // Zrób zrzut ekranu w przypadku błędu
      try {
        if (this.page) {
          await this.page.screenshot({ path: 'pagination-error.png' });
          await this.logScraperAction('info', 'Zapisano zrzut ekranu błędu do pagination-error.png');
        }
      } catch (screenshotError) {
        await this.logScraperAction('error', `Nie udało się zrobić zrzutu ekranu: ${screenshotError instanceof Error ? screenshotError.message : String(screenshotError)}`);
      }
      
      return false;
    }
  }

  // Pomocnicza metoda do tworzenia URL z numerem strony
  private createPageUrl(baseUrl: string, pageNumber: number): string {
    // Usuń istniejący parametr page, jeśli istnieje
    const urlObj = new URL(baseUrl, 'https://www.otomoto.pl'); // Dodajemy base URL, jeśli url jest względny
    
    // Usuń istniejący parametr page
    urlObj.searchParams.delete('page');
    
    // Dodaj nowy parametr page
    urlObj.searchParams.set('page', pageNumber.toString());
    
    return urlObj.toString();
  }

  // Metoda obsługująca ponawianie próby w przypadku błędów
  private async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000,
    operationName: string = 'Operacja'
  ): Promise<T> {
    let lastError: Error = new Error('Unknown error');

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Sprawdź, czy błąd dotyczy odłączonej ramki
        const errorMessage = error instanceof Error ? error.message : String(error);
        const isDetachedFrameError = errorMessage.includes('detached Frame');
        
        // Zwiększ ogólny licznik błędów
        this.errorCount++;
        
        // Sprawdź, czy przekroczyliśmy maksymalną liczbę błędów w całym procesie
        if (this.errorCount >= this.maxTotalErrorRetries) {
          await this.logScraperAction('error', `Przekroczono maksymalną liczbę błędów (${this.maxTotalErrorRetries}) w całym procesie scrapowania. Przerywanie.`);
          
          // Zatrzymaj proces scrapowania
          if (this.browser) {
            await this.close();
          }
          
          // Rzuć błąd z informacją o przekroczeniu limitu
          throw new Error(`Zatrzymano scrapowanie z powodu zbyt dużej liczby błędów (${this.errorCount}/${this.maxTotalErrorRetries})`);
        }
        
        // Jeśli to błąd odłączonej ramki, a już mieliśmy za dużo takich błędów
        if (isDetachedFrameError && this.errorCount > 3) {
          await this.logScraperAction('error', `Zbyt wiele błędów odłączonej ramki (${this.errorCount}). Przerywanie operacji: ${operationName}`);
          throw new Error(`Zatrzymano ${operationName} z powodu powtarzających się błędów odłączonej ramki`);
        }
        
        // Jeśli to ostatnia próba, propaguj błąd
        if (attempt === maxRetries) {
          await this.logScraperAction('error', `${operationName} nie powiodła się po ${maxRetries} próbach: ${errorMessage}`);
          throw error;
        }
        
        // Dla błędów z detached Frame, dodajemy dłuższy czas oczekiwania
        let waitTime = delayMs * Math.pow(2, attempt - 1);
        
        if (isDetachedFrameError) {
          waitTime = delayMs * Math.pow(3, attempt - 1); // Dłuższe oczekiwanie dla tego typu błędu
          await this.logScraperAction('warn', `Błąd odłączonej ramki w ${operationName} (próba ${attempt}/${maxRetries}), ponowienie za ${waitTime}ms: ${errorMessage}`);
        } else {
          await this.logScraperAction('warn', `${operationName} nie powiodła się (próba ${attempt}/${maxRetries}), ponowienie za ${waitTime}ms: ${errorMessage}`);
        }
        
        // Poczekaj przed ponowieniem próby
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    // Ten kod nigdy nie powinien zostać osiągnięty, ale TS wymaga zwrócenia wartości
    throw lastError;
  }

  // Nowa metoda do scrapowania w trybie turbo z użyciem Cheerio
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async scrapeAndSaveTurbo(url: string, _sessionId?: number): Promise<{ success: boolean; message: string; adsCount: number }> {
    let savedAdsCount = 0;
    let scrapingSession: ScrapingSession | null = null;
    
    // Import funkcji zarządzających ID ogłoszeń
    const idManager = await import('./turbo/id-manager');
    
    // Inicjalizacja helpera Cheerio
    const cheerioHelper = new CheerioHelper({
      maxConcurrent: 5, // Maksymalna liczba równoczesnych zapytań
      minDelayMs: 100,  // Minimalne opóźnienie między zapytaniami
      maxDelayMs: 300   // Maksymalne opóźnienie między zapytaniami
    });
    
    try {
      await this.logScraperAction('info', 'Rozpoczynanie procesu scrapowania w trybie TURBO');
      
      // Upewnij się, że URL zawiera parametr page=1 jeśli jeszcze go nie ma
      let baseUrl = url;
      if (!url.includes('page=')) {
        baseUrl = url + (url.includes('?') ? '&page=1' : '?page=1');
        await this.logScraperAction('info', `Dodano parametr page=1 do bazowego URL: ${baseUrl}`);
      } else {
        await this.logScraperAction('info', `Bazowy URL już zawiera parametr page: ${baseUrl}`);
      }
      
      // Sprawdzenie liczby stron za pomocą Cheerio - dodajemy dodatkowe próby
      let pagesCount = 1;
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          pagesCount = await cheerioHelper.getNumberOfPages(baseUrl);
          await this.logScraperAction('info', `Wykryto ${pagesCount} stron do scrapowania (próba ${attempt})`);
          
          // Jeśli wykryto więcej niż 1 stronę, możemy przerwać pętlę
          if (pagesCount > 1) {
            break;
          }
          
          // Jeśli wykryto tylko 1 stronę, a jesteśmy w trybie manualnym, spróbujmy ponownie
          if (attempt < 3) {
            await this.logScraperAction('info', `Wykryto tylko 1 stronę - ponowna próba ${attempt+1}/3 po 2s`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } catch (error) {
          await this.logScraperAction('error', `Błąd podczas wykrywania liczby stron (próba ${attempt}): ${error instanceof Error ? error.message : String(error)}`);
          // Próbuj dalej, jeśli to nie ostatnia próba
          if (attempt < 3) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }
      
      // Pobierz ustawienia scrapera z bazy danych
      const scraperSettings = await prisma.scraperSettings.findFirst();
      const maxPages = scraperSettings?.maxPages ?? -1; // domyślna wartość oznaczająca "wszystkie strony"

      // Ogranicz liczbę stron jeśli potrzeba (implementacja limitu stron w trybie turbo)
      const totalPages = (maxPages > 0 && maxPages < pagesCount) ? maxPages : pagesCount;
      await this.logScraperAction('info', `Zaplanowano scrapowanie ${totalPages} stron (limit: ${maxPages === -1 ? 'brak' : maxPages})`);
      
      // Utwórz nową sesję scrapowania
      scrapingSession = await prisma.scrapingSession.create({
        data: {
          scrapingUrl: url,
          totalPages: totalPages,
          status: 'in_progress'
        }
      });
      
      await this.logScraperAction('info', `Utworzono sesję scrapowania (ID: ${scrapingSession.id})`);
      
      // Kolekcja do przechowywania wszystkich ID ogłoszeń
      let allAdIds: string[] = [];
      
      // Synchronizuj z globalnym stanem (wczytaj zapisane ID jeśli istnieją)
      const previouslyScrapedIds = await idManager.syncWithGlobalState();
      
      // Pierwsza faza: Zbieranie ID ogłoszeń ze wszystkich stron
      for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
        try {
          const pageUrl = this.createPageUrl(url, currentPage);
          await this.logScraperAction('info', `[Faza 1] Pobieranie ID ogłoszeń ze strony ${currentPage}/${totalPages}: ${pageUrl}`);
          
          // Aktualizuj status sesji scrapowania
          if (scrapingSession) {
            await prisma.scrapingSession.update({
              where: { id: scrapingSession.id },
              data: { currentPage }
            });
          }
          
          // Zbierz ID ogłoszeń z bieżącej strony
          const pageAdIds = await cheerioHelper.scrapeAdIds(pageUrl);
          
          // Dodaj do listy lokalnej
          allAdIds.push(...pageAdIds);
          
          // Dodaj do globalnego stanu
          idManager.addToGlobalState(pageAdIds);
          
          await this.logScraperAction('info', `[Faza 1] Pobrano ${pageAdIds.length} ID ogłoszeń ze strony ${currentPage}`);
          
          // Aktualizuj liczbę zebranych ID
          if (scrapingSession) {
            await prisma.scrapingSession.update({
              where: { id: scrapingSession.id },
              data: { 
                adsIdsScraped: allAdIds.length,
                totalAdsIds: allAdIds.length
              }
            });
          }
          
          // Dodaj losowe opóźnienie przed przejściem do następnej strony (krótsze w trybie turbo)
          if (currentPage < totalPages) {
            const delayTime = 100 + Math.random() * 200;
            await this.logScraperAction('info', `[Faza 1] Oczekiwanie ${Math.round(delayTime)}ms przed przejściem do następnej strony...`);
            await new Promise(resolve => setTimeout(resolve, delayTime));
          }
        } catch (error) {
          await this.logScraperAction('error', `[Faza 1] Błąd podczas pobierania ID ogłoszeń ze strony ${currentPage}: ${error instanceof Error ? error.message : String(error)}`);
          // Kontynuuj mimo błędu - próbuj następnych stron
        }
      }
      
      await this.logScraperAction('info', `[Faza 1] Zebrano łącznie ${allAdIds.length} unikalnych ID ogłoszeń ze wszystkich stron`);
      
      // Sprawdź, czy mamy już wcześniej zescrapowane ID
      if (previouslyScrapedIds.length > 0) {
        await this.logScraperAction('info', `Wykryto ${previouslyScrapedIds.length} wcześniej zescrapowanych ID ogłoszeń`);
        
        // Filtruj ID, aby uniknąć ponownego przetwarzania
        const newIds = idManager.filterNewIds(allAdIds, previouslyScrapedIds);
        
        if (newIds.length < allAdIds.length) {
          await this.logScraperAction('info', `Pominięto ${allAdIds.length - newIds.length} już zescrapowanych ID, pozostało ${newIds.length} do przetworzenia`);
          allAdIds = newIds;
        }
      }
      
      // Aktualizuj status sesji po zakończeniu fazy 1
      if (scrapingSession) {
        await prisma.scrapingSession.update({
          where: { id: scrapingSession.id },
          data: { 
            adsIdsScraped: allAdIds.length + previouslyScrapedIds.length,
            totalAdsIds: allAdIds.length + previouslyScrapedIds.length
          }
        });
      }
      
      // Druga faza: Pobieranie szczegółów ogłoszeń i numerów telefonów
      await this.logScraperAction('info', `[Faza 2] Rozpoczynam pobieranie szczegółów i numerów telefonów dla ${allAdIds.length} ogłoszeń`);
      
      let adsDetailsScraped = 0;
      let adsWithPhones = 0;
      
      // Uwzględnij wcześniejsze wyniki w licznikach
      if (scrapingSession && scrapingSession.adsDetailsScraped > 0) {
        adsDetailsScraped = scrapingSession.adsDetailsScraped;
      }
      if (scrapingSession && scrapingSession.adsWithPhones > 0) {
        adsWithPhones = scrapingSession.adsWithPhones;
      }
      
      // Przetwarzaj ogłoszenia w porcjach, aby ograniczyć obciążenie
      const batchSize = 10; // Liczba ogłoszeń w jednej porcji
      const batches = idManager.createBatches(allAdIds, batchSize);
      
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        const batchPromises = batch.map(adId => {
          return new Promise<void>(async (resolve) => {
            try {
              // Pobierz szczegóły ogłoszenia
              const apiData = await cheerioHelper.enqueue(() => cheerioHelper.fetchAdDetails(adId));
              
              // Sprawdź czy ogłoszenie nie zostało oznaczone do pominięcia z powodu województwa
              if (apiData && apiData.ignoredByRegion) {
                await this.logScraperAction('info', `[Faza 2] Pomijanie ogłoszenia ${adId} z lokalizacją ${apiData.location} - niedozwolone województwo`);
                return; // Kończymy tę funkcję zamiast używać continue
              }
              
              // Zamapuj dane API na strukturę AdData
              const ad = this.mapApiDataToAdData(apiData, adId);
              adsDetailsScraped++;
              
              // Jeśli ogłoszenie zostało oznaczone do pominięcia podczas mapowania
              if (ad.brandModel === 'Auto (pominięto - złe województwo)') {
                await this.logScraperAction('info', `[Faza 2] Pomijanie ogłoszenia ${adId} z lokalizacją ${ad.location} - po mapowaniu`);
                return; // Kończymy tę funkcję zamiast używać continue
              }
              
              // Pobierz numery telefonów
              try {
                const phones = await cheerioHelper.enqueue(() => cheerioHelper.fetchAdPhones(adId));
                
                if (phones && phones.length > 0) {
                  ad.phones = phones;
                  adsWithPhones++;
                  
                  // Zapisz ogłoszenie do bazy
                  const saved = await this.saveAdToDatabase(ad);
                  if (saved) {
                    savedAdsCount++;
                  }
                }
              } catch (phoneError) {
                await this.logScraperAction('error', `Błąd podczas pobierania numerów telefonów dla ${adId}: ${phoneError instanceof Error ? phoneError.message : String(phoneError)}`);
              }
              
              // Aktualizuj sesję co 5 ogłoszeń dla lepszej wydajności
              if (adsDetailsScraped % 5 === 0 && scrapingSession) {
                await prisma.scrapingSession.update({
                  where: { id: scrapingSession.id },
                  data: {
                    adsDetailsScraped,
                    adsWithPhones
                  }
                });
              }
            } catch (adError) {
              await this.logScraperAction('error', `Błąd podczas przetwarzania ogłoszenia ${adId}: ${adError instanceof Error ? adError.message : String(adError)}`);
            } finally {
              resolve();
            }
          });
        });
        
        // Przetwarzaj porcję ogłoszeń równolegle
        await Promise.all(batchPromises);
        
        // Aktualizuj status sesji po każdej porcji
        if (scrapingSession) {
          await prisma.scrapingSession.update({
            where: { id: scrapingSession.id },
            data: { 
              adsDetailsScraped,
              adsWithPhones
            }
          });
        }
        
        // Log postępu co pewną liczbę porcji
        const processedCount = Math.min((batchIndex + 1) * batchSize, allAdIds.length);
        await this.logScraperAction('info', `[Faza 2] Przetworzono ${processedCount}/${allAdIds.length} ogłoszeń (${adsWithPhones} z numerami telefonu)`);
      }
      
      // Oznacz sesję jako zakończoną
      if (scrapingSession && scrapingSession.id) {
        await prisma.scrapingSession.update({
          where: { id: scrapingSession.id },
          data: { 
            status: 'completed',
            finishedAt: new Date()
          }
        });
      }
      
      const resultMessage = `TRYB TURBO: Pomyślnie zescrapowano ${allAdIds.length} ID ogłoszeń z ${totalPages} stron. `
        + `Pobrano szczegóły dla ${adsDetailsScraped} ogłoszeń, z czego ${adsWithPhones} miało numery telefonów. `
        + `Zapisano ${savedAdsCount} nowych ogłoszeń.`;
      
      await this.logScraperAction('info', `Scrapowanie zakończone: ${resultMessage}`);
      
      return {
        success: true,
        message: resultMessage,
        adsCount: savedAdsCount,
      };
    } catch (error) {
      // W przypadku błędu oznacz sesję jako przerwaną
      if (scrapingSession && scrapingSession.id) {
        try {
          await prisma.scrapingSession.update({
            where: { id: scrapingSession.id },
            data: { 
              status: 'interrupted',
              finishedAt: new Date()
            }
          });
        } catch (updateError) {
          await this.logScraperAction('error', `Błąd podczas aktualizacji statusu sesji: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
        }
      }
      
      const errorMessage = `Błąd podczas scrapowania w trybie TURBO: ${error instanceof Error ? error.message : String(error)}`;
      await this.logScraperAction('error', errorMessage);
      
      return {
        success: false,
        message: errorMessage,
        adsCount: savedAdsCount,
      };
    }
  }

  async scrapeAndSave(url: string, sessionId?: number): Promise<{ success: boolean; message: string; adsCount: number }> {
    // Sprawdź, czy mamy używać trybu turbo
    const scraperSettings = await prisma.scraperSettings.findFirst();
    if (scraperSettings?.turboMode) {
      await this.logScraperAction('info', 'Wykryto włączony tryb TURBO - używam zoptymalizowanej metody scrapowania');
      return this.scrapeAndSaveTurbo(url, sessionId);
    }
    
    let totalAdsCount = 0;
    let savedAdsCount = 0;
    
    try {
      await this.logScraperAction('info', 'Rozpoczynanie procesu scrapowania w trybie standardowym');
      await this.initialize();
      await this.logScraperAction('info', 'Scraper zainicjalizowany');
      
      // Pobierz ustawienia scrapera z bazy danych
      const scraperSettings = await prisma.scraperSettings.findFirst();
      // Użyj bezpiecznego dostępu do maxPages - w przypadku braku pola użyj domyślnej wartości -1
      const maxPages = scraperSettings?.maxPages ?? -1; // domyślna wartość oznaczająca "wszystkie strony"
      
      // Upewnij się, że URL zawiera parametr page=1 jeśli jeszcze go nie ma
      let baseUrl = url;
      if (!url.includes('page=')) {
        baseUrl = url + (url.includes('?') ? '&page=1' : '?page=1');
        await this.logScraperAction('info', `Dodano parametr page=1 do bazowego URL: ${baseUrl}`);
      } else {
        await this.logScraperAction('info', `Bazowy URL już zawiera parametr page: ${baseUrl}`);
      }
      
      // Przejdź do pierwszej strony URL
      const firstPageUrl = baseUrl;
      await this.logScraperAction('info', `Przechodzenie do pierwszej strony: ${firstPageUrl}`);
      
      // Użyj mechanizmu retry dla przejścia do strony
      await this.withRetry(
        async () => {
          await this.page!.goto(firstPageUrl, { 
            waitUntil: 'networkidle2', 
            timeout: 60000 // Zwiększony timeout dla początkowego ładowania
          });
        },
        3, 
        2000,
        'Ładowanie pierwszej strony'
      );
      
      // Akceptuj cookies
      await this.logScraperAction('info', 'Akceptowanie cookies');
      await this.acceptCookies();
      
      // Sprawdź liczbę stron
      let pagesCount = await this.checkNumberOfPages();
      
      // Jeśli ustawiono maxPages, ogranicz liczbę stron do scrapowania
      if (maxPages > 0 && maxPages < pagesCount) {
        await this.logScraperAction('info', `Ograniczenie liczby stron do ${maxPages} (z wykrytych ${pagesCount})`);
        pagesCount = maxPages;
      } else {
        await this.logScraperAction('info', `Scrapowanie wszystkich ${pagesCount} stron`);
      }
      
      // Scrapuj każdą stronę używając parametru page w URL zamiast nawigacji klikaniem
      let currentPage = 1;
      
      while (currentPage <= pagesCount) {
        await this.logScraperAction('info', `Scrapowanie strony ${currentPage}/${pagesCount}`);
        
        // Aktualizuj status scrapowania w globalnym obiekcie
        if (global.cronFunctions && typeof global.cronFunctions.updateScrapingStatus === 'function') {
          global.cronFunctions.updateScrapingStatus(currentPage);
        }
        
        // Jeśli nie jest to pierwsza strona, przejdź do kolejnej używając parametru page w URL
        if (currentPage > 1) {
          const pageUrl = this.createPageUrl(url, currentPage);
          await this.logScraperAction('info', `Przechodzenie do strony ${currentPage} przez URL: ${pageUrl}`);
          
          try {
            // Użyj mechanizmu retry dla przejścia do kolejnej strony
            // z bardziej zaawansowaną obsługą nawigacji
            await this.withRetry(
              async () => {
                // Podejście 1: Najpierw poczekaj na załadowanie DOM, a następnie na ustabilizowanie sieci
                await this.page!.goto(pageUrl, { 
                  waitUntil: 'domcontentloaded', // najpierw czekaj na DOM
                  timeout: 60000 // zwiększony timeout
                });
                
                // Poczekaj chwilę po załadowaniu DOM
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Następnie poczekaj na networkidle2, ale ignoruj błędy timeout
                try {
                  await this.page!.waitForNavigation({ 
                    waitUntil: 'networkidle2',
                    timeout: 10000 
                  });
                } catch (navError) {
                  // Ignoruj błędy timeout - strona może być już załadowana
                  this.logScraperAction('info', `Ignorowanie timeout dla networkidle2, strona już może być załadowana: ${navError instanceof Error ? navError.message : String(navError)}`);
                }
              },
              3,
              3000, // zwiększony odstęp między ponowieniami
              `Ładowanie strony ${currentPage}`
            );
            
            // Dłuższy czas oczekiwania po nawigacji, aby dać stronie czas na pełne załadowanie
            await new Promise(resolve => setTimeout(resolve, 3000));
            
          } catch (pageError) {
            await this.logScraperAction('error', `Nie udało się przejść do strony ${currentPage} - pomijanie: ${pageError instanceof Error ? pageError.message : String(pageError)}`);
            currentPage++;
            continue;
          }
        }
        
        // Upewnij się, że status scrapera jest aktualizowany po każdej stronie
        if (global.cronFunctions && typeof global.cronFunctions.updateScrapingStatus === 'function') {
          global.cronFunctions.updateScrapingStatus(currentPage);
        }
        
        try {
          // Scrapuj aktualną stronę
          const ads = await this.scrapeListingPage(
            currentPage === 1 ? firstPageUrl : this.createPageUrl(url, currentPage)
          );
          await this.logScraperAction('info', `Pobrano ${ads.length} ogłoszeń ze strony ${currentPage}`);
          
          // Zapisz do bazy danych i zwiększ licznik
          const savedOnThisPage = await this.saveAdsToDatabase(ads);
          savedAdsCount += savedOnThisPage;
          totalAdsCount += ads.length;
        } catch (pageScrapingError) {
          await this.logScraperAction('error', `Błąd podczas scrapowania strony ${currentPage}: ${pageScrapingError instanceof Error ? pageScrapingError.message : String(pageScrapingError)}`);
          // Kontynuuj z następną stroną mimo błędu na bieżącej
        }
        
        // Przejdź do następnej strony
        currentPage++;
        
        // Dodaj opóźnienie przed przejściem do następnej strony (w trybie turbo znacznie mniejsze)
        if (currentPage <= pagesCount) {
          const delayTime = this.settings.turboMode ? 100 : 1000 + Math.random() * 1000;
          await this.logScraperAction('info', `Oczekiwanie ${Math.round(delayTime)}ms przed przejściem do następnej strony...`);
          await new Promise(resolve => setTimeout(resolve, delayTime));
        }
      }
      
      // Zakończ scrapowanie
      await this.logScraperAction('info', 'Zamykanie przeglądarki');
      await this.close();
      
      const resultMessage = `Pomyślnie zescrapowano ${totalAdsCount} ogłoszeń z ${pagesCount} stron. Zapisano ${savedAdsCount} nowych ogłoszeń.`;
      await this.logScraperAction('info', `Scrapowanie zakończone: ${resultMessage}`);
      
      return {
        success: true,
        message: resultMessage,
        adsCount: savedAdsCount,
      };
    } catch (error) {
      const errorMessage = `Błąd podczas scrapowania: ${error instanceof Error ? error.message : String(error)}`;
      await this.logScraperAction('error', errorMessage);
      
      // Zamknij przeglądarkę w przypadku błędu
      try {
        await this.close();
      } catch (closeError) {
        await this.logScraperAction('error', `Błąd podczas zamykania przeglądarki: ${closeError instanceof Error ? closeError.message : String(closeError)}`);
      }
      
      return {
        success: false,
        message: errorMessage,
        adsCount: savedAdsCount,
      };
    } finally {
      // Upewnij się, że przywrócono status UI niezależnie od wyniku
      if (global.cronFunctions && typeof global.cronFunctions.updateScrapingStatus === 'function') {
        global.cronFunctions.updateScrapingStatus(0); // 0 oznacza brak aktywnego scrapowania
      }
    }
  }
}
