import { PrismaClient, ScrapingSession } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Zapisuje listę zescrapowanych ID ogłoszeń do bazy danych
 * @param sessionId ID sesji scrapowania
 * @param adIds Lista zescrapowanych ID ogłoszeń
 */
export async function saveScrapedAdIds(sessionId: number, adIds: string[]): Promise<void> {
  try {
    await prisma.scrapingSession.update({
      where: { id: sessionId },
      data: {
        scrapedAdIds: JSON.stringify(adIds)
      }
    });
    console.log(`Zapisano ${adIds.length} ID ogłoszeń do sesji ${sessionId}`);
  } catch (error) {
    console.error('Błąd podczas zapisywania ID ogłoszeń:', error);
    throw error;
  }
}

/**
 * Wczytuje listę zescrapowanych ID ogłoszeń z bazy danych
 * @param sessionId ID sesji scrapowania
 * @returns Lista zescrapowanych ID ogłoszeń
 */
export async function loadScrapedAdIds(sessionId: number): Promise<string[]> {
  try {
    const session = await prisma.scrapingSession.findUnique({
      where: { id: sessionId }
    });
    
    if (session?.scrapedAdIds) {
      try {
        const adIds = JSON.parse(session.scrapedAdIds);
        console.log(`Wczytano ${adIds.length} ID ogłoszeń z sesji ${sessionId}`);
        return adIds;
      } catch (e) {
        console.error('Błąd podczas parsowania zapisanych ID:', e);
      }
    } else {
      console.log(`Brak zapisanych ID ogłoszeń dla sesji ${sessionId}`);
    }
    return [];
  } catch (error) {
    console.error('Błąd podczas wczytywania ID ogłoszeń:', error);
    return [];
  }
}

/**
 * Sprawdza, czy istnieje aktywna sesja scrapowania
 * @returns true, jeśli istnieje aktywna sesja, false w przeciwnym razie
 */
export async function hasActiveScrapingSession(): Promise<boolean> {
  try {
    const activeSessions = await prisma.scrapingSession.findMany({
      where: {
        OR: [
          { status: 'in_progress' },
          { status: 'collecting_ids' },
          { status: 'collecting_details' }
        ]
      }
    });
    
    return activeSessions.length > 0;
  } catch (error) {
    console.error('Błąd podczas sprawdzania aktywnych sesji:', error);
    return false;
  }
}

/**
 * Weryfikuje, że tylko jedna sesja scrapowania może być aktywna
 * @returns true, jeśli możliwe jest uruchomienie nowej sesji, false w przeciwnym razie
 */
export async function ensureSingleActiveSession(): Promise<boolean> {
  return !(await hasActiveScrapingSession());
}

/**
 * Pobiera aktywną sesję scrapowania lub ostatnią zakończoną
 * @returns Sesja scrapowania lub null, jeśli nie istnieje
 */
export async function getCurrentScrapingSession(): Promise<ScrapingSession | null> {
  try {
    // Najpierw sprawdź, czy istnieje sesja w trakcie
    let session = await prisma.scrapingSession.findFirst({
      where: {
        OR: [
          { status: 'in_progress' },
          { status: 'collecting_ids' },
          { status: 'collecting_details' },
          { status: 'paused' }
        ]
      },
      orderBy: {
        startedAt: 'desc'
      }
    });

    // Jeśli nie ma aktywnej sesji, pobierz ostatnią zakończoną
    if (!session) {
      session = await prisma.scrapingSession.findFirst({
        orderBy: {
          startedAt: 'desc'
        }
      });
    }

    return session;
  } catch (error) {
    console.error('Błąd podczas pobierania aktualnej sesji scrapowania:', error);
    return null;
  }
}

/**
 * Aktualizuje flagę stanu scrapowania w globalnych funkcjach cron
 * @param isActive true, jeśli scrapowanie jest aktywne, false w przeciwnym razie
 * @param currentPage aktualna strona scrapowania (0 oznacza brak aktywnego scrapowania)
 */
export function updateScrapingStatus(isActive: boolean, currentPage: number = 0): void {
  if (global.cronFunctions && typeof global.cronFunctions.updateScrapingStatus === 'function') {
    global.cronFunctions.updateScrapingStatus(isActive ? currentPage : 0);
  }
}

/**
 * Pauzuje wszystkie aktywne sesje scrapowania
 */
export async function pauseAllActiveScrapingSessions(): Promise<void> {
  try {
    await prisma.scrapingSession.updateMany({
      where: {
        OR: [
          { status: 'in_progress' },
          { status: 'collecting_ids' },
          { status: 'collecting_details' }
        ]
      },
      data: {
        status: 'paused',
        pausedAt: new Date(),
        pauseDuration: 600, // 10 minut pauzy
        pauseReason: 'system_pause' // Wskazujemy, że to systemowa pauza
      }
    });
    
    // Aktualizuj stan globalny
    updateScrapingStatus(false, 0);
    
    console.log('Wszystkie aktywne sesje scrapowania zostały wstrzymane');
  } catch (error) {
    console.error('Błąd podczas pauzowania aktywnych sesji:', error);
  }
}
