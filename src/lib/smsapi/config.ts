import https from 'https';
import fs from 'fs';
import path from 'path';

export const xmlHeader = {
  transactionId: 1,
  timestamp: new Date(Date.now()).toISOString(),
  nws: 48882901100,
  MSISDN: 48606666320,
};

export const xmlBuilderOptions = {
  processEntities: false,
  format: true,
  ignoreAttributes: false,
  suppressEmptyNode: true,
  arrayNodeName: 'recipient',
};

// Sprawdź czy certyfikaty istnieją
const certPath = path.join(process.cwd(), './src/lib/smsapi/certificates/sup-48606666320.pem');
const certExists = fs.existsSync(certPath);

// Użyj certyfikatu do komunikacji z bramką SMS
export const reqHttpsAgent = certExists
  ? new https.Agent({
      rejectUnauthorized: false, // Możesz ustawić na true, jeśli serwer ma zaufany certyfikat
      cert: fs.readFileSync(certPath), // Certyfikat w formacie PEM
      key: fs.readFileSync(certPath), // Klucz w formacie PEM (ten sam plik zawiera zarówno certyfikat jak i klucz)
    })
  : undefined;

export const smsApiEndpoint = 'https://superinfo.t-mobile.pl/superinfoapi2';
