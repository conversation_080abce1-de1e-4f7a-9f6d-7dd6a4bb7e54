import axios from 'axios';
import { XMLBuilder, XMLParser } from 'fast-xml-parser';
import {
  reqHttpsAgent,
  xmlBuilderOptions,
  xmlHeader,
  smsApiEndpoint
} from './config';

interface SmsRecipient {
  phone: string | number;
  content: string;
}

class SmsApiService {
  private xmlParser: XMLParser;
  private xmlBuilder: XMLBuilder;

  constructor() {
    this.xmlParser = new XMLParser();
    this.xmlBuilder = new XMLBuilder(xmlBuilderOptions);
  }

  /**
   * Wysyła SMS-y do wielu odbiorców
   * @param smsList Lista odbiorców i treści SMS-ów
   * @returns Odpowiedź z bramki SMS
   */
  async createSmsShipment(smsList: SmsRecipient[]): Promise<any> {
    if (!reqHttpsAgent) {
      throw new Error('Brak certyfikatu do bramki SMS. <PERSON><PERSON><PERSON>ć certyfikat w katalogu src/lib/smsapi/certificates/');
    }

    const shipmentXML = {
      'sup2:SupRequest': {
        '@_xsi:schemaLocation="http://superinfo.t-mobile.pl/api2 api-supera2.xsd"': true,
        '@_xmlns:sup2="http://superinfo.t-mobile.pl/api2"': true,
        '@_xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"': true,
        header: xmlHeader,
        createShipment: {
          sender: {
            nws: '',
          },
          messages: {
            multiContent: {
              recipient: smsList.map((sms) => {
                return {
                  MSISDN: sms.phone,
                  content: sms.content,
                };
              }),
            },
          },
          ttl: 'DAY',
          normalize: true,
        },
      },
    };

    const xmlContent = this.buildXmlFromJson(shipmentXML);

    // Dodane logowanie treści XML przed wysłaniem
    console.debug('Wysyłanie żądania XML do bramki SMS:', xmlContent);

    try {
      const response = await axios.post(smsApiEndpoint, xmlContent, {
        headers: {
          'Content-Type': 'application/xml',
        },
        httpsAgent: reqHttpsAgent,
      });

      return this.parseXml(response.data);
    } catch (error) {
      console.error('Błąd podczas wysyłania SMS:', error);
      throw error;
    }
  }

  /**
   * Sprawdza status bramki SMS
   * @returns Status bramki SMS
   */
  async pingSmsApi(): Promise<any> {
    if (!reqHttpsAgent) {
      return {
        status: 'error',
        message: 'Brak certyfikatu do bramki SMS. Umieść certyfikat w katalogu src/lib/smsapi/certificates/'
      };
    }

    const json = {
      'sup2:SupRequest': {
        '@_xsi:schemaLocation': 'http://superinfo.t-mobile.pl/api2 api-supera2.xsd',
        '@_xmlns:sup2': 'http://superinfo.t-mobile.pl/api2',
        '@_xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
        header: xmlHeader,
        ping: '',
      },
    };

    const xmlContent = this.buildXmlFromJson(json);

    try {
      console.log('Wysyłanie żądania ping do bramki SMS...');
      const response = await axios.post(smsApiEndpoint, xmlContent, {
        headers: {
          'Content-Type': 'application/xml',
        },
        httpsAgent: reqHttpsAgent,
      });

      const parsedResult = this.parseXml(response.data);
      return {
        status: 'ok',
        data: parsedResult['ns2:SupResponse']?.header || parsedResult
      };
    } catch (error) {
      console.error('Błąd podczas pingowania bramki SMS:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Nieznany błąd'
      };
    }
  }

  /**
   * Buduje XML z obiektu JSON
   * @param json Obiekt JSON do zbudowania XML
   * @returns Zbudowany XML
   */
  buildXmlFromJson(json: any): string {
    return this.xmlBuilder.build(json);
  }

  /**
   * Parsuje XML do JSON
   * @param xml XML do sparsowania
   * @returns Sparsowany JSON
   */
  parseXml(xml: string | Buffer): any {
    return this.xmlParser.parse(xml);
  }
}

// Eksportuj singleton
export const smsApiService = new SmsApiService();
