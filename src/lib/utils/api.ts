/**
 * Funkcje pomocnicze do obsługi żądań API
 */

/**
 * Wykonuje żądanie API z dodanym tokenem autoryzacji
 * @param url URL endpointu API
 * @param options Opcje żądania fetch
 * @returns Odpowiedź z API
 */
export async function fetchWithAuth(url: string, options: RequestInit = {}) {
  let token = null;
  let cookieToken = '';
  
  try {
    // Pobierz token z localStorage
    token = localStorage.getItem('token');
  } catch (error) {
    console.warn('Nie można uzyskać dostępu do localStorage:', error);
  }
  
  try {
    // Pobierz token z ciasteczka (jeśli dostępny)
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'auth_token') {
        cookieToken = value;
        break;
      }
    }
  } catch (error) {
    console.warn('Nie można uzyskać dostępu do ciasteczek:', error);
  }
  
  // Użyj tokenu z localStorage lub z ciasteczka
  const authToken = token || cookieToken;
  
  if (!authToken) {
    console.warn('Brak tokenu uwierzytelniającego. Żądanie może zostać odrzucone.');
  }
  
  // Przygotuj nagłówki z tokenem autoryzacji
  const headers = {
    'Content-Type': 'application/json',
    ...(authToken ? { 'Authorization': `Bearer ${authToken}` } : {}),
    ...options.headers,
  };
  
  // Wykonaj żądanie z dodanymi nagłówkami
  return fetch(url, {
    ...options,
    headers,
  }).then(response => {
    // Jeśli odpowiedź to 401 (Unauthorized), przekieruj na stronę logowania
    if (response.status === 401) {
      console.error('Błąd uwierzytelniania (401). Przekierowuję na stronę logowania.');
      window.location.href = '/login';
      return Promise.reject(new Error('Sesja wygasła. Zaloguj się ponownie.'));
    }
    return response;
  });
}

/**
 * Wykonuje żądanie GET do API z dodanym tokenem autoryzacji
 * @param url URL endpointu API
 * @param options Opcje żądania fetch
 * @returns Odpowiedź z API
 */
export async function getFromApi(url: string, options: RequestInit = {}) {
  return fetchWithAuth(url, {
    method: 'GET',
    ...options,
  });
}

/**
 * Wykonuje żądanie POST do API z dodanym tokenem autoryzacji
 * @param url URL endpointu API
 * @param data Dane do wysłania
 * @param options Opcje żądania fetch
 * @returns Odpowiedź z API
 */
export async function postToApi(url: string, data: any, options: RequestInit = {}) {
  return fetchWithAuth(url, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
}

/**
 * Wykonuje żądanie PUT do API z dodanym tokenem autoryzacji
 * @param url URL endpointu API
 * @param data Dane do wysłania
 * @param options Opcje żądania fetch
 * @returns Odpowiedź z API
 */
export async function putToApi(url: string, data: any, options: RequestInit = {}) {
  return fetchWithAuth(url, {
    method: 'PUT',
    body: JSON.stringify(data),
    ...options,
  });
}

/**
 * Wykonuje żądanie DELETE do API z dodanym tokenem autoryzacji
 * @param url URL endpointu API
 * @param options Opcje żądania fetch
 * @returns Odpowiedź z API
 */
export async function deleteFromApi(url: string, options: RequestInit = {}) {
  return fetchWithAuth(url, {
    method: 'DELETE',
    ...options,
  });
}
