import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Łączy klasy CSS z różnych źródeł, rozwiązując konflikty za pomocą tailwind-merge
 * @param inputs Klasy CSS do połączenia
 * @returns Połączone klasy CSS
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formatuje cenę do wyświetlenia
 * @param price Cena do sformatowania
 * @param currency Waluta (domyślnie PLN)
 * @returns Sformatowana cena
 */
export function formatPrice(price: number, currency: string = "PLN") {
  return new Intl.NumberFormat("pl-PL", {
    style: "currency",
    currency,
  }).format(price);
}

/**
 * Formatuje datę do wyświetlenia
 * @param date Data do sformatowania
 * @returns Sformatowana data
 */
export function formatDate(date: Date | string) {
  if (typeof date === "string") {
    date = new Date(date);
  }
  
  return new Intl.DateTimeFormat("pl-PL", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }).format(date);
}

/**
 * Formatuje datę i czas do wyświetlenia
 * @param date Data do sformatowania
 * @returns Sformatowana data i czas
 */
export function formatDateTime(date: Date | string) {
  if (typeof date === "string") {
    date = new Date(date);
  }
  
  return new Intl.DateTimeFormat("pl-PL", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

/**
 * Formatuje numer telefonu do wyświetlenia
 * @param phone Numer telefonu do sformatowania
 * @returns Sformatowany numer telefonu
 */
export function formatPhone(phone: string) {
  if (!phone) return "";
  
  // Usuń wszystkie znaki niebędące cyframi
  const digits = phone.replace(/\D/g, "");
  
  // Jeśli numer ma 9 cyfr, sformatuj jako XXX-XXX-XXX
  if (digits.length === 9) {
    return digits.replace(/(\d{3})(\d{3})(\d{3})/, "$1-$2-$3");
  }
  
  // Jeśli numer ma 11 cyfr i zaczyna się od 48 (Polska), sformatuj jako +48 XXX-XXX-XXX
  if (digits.length === 11 && digits.startsWith("48")) {
    return `+48 ${digits.substring(2).replace(/(\d{3})(\d{3})(\d{3})/, "$1-$2-$3")}`;
  }
  
  // W przeciwnym razie zwróć oryginalny numer
  return phone;
}

/**
 * Skraca tekst do określonej długości
 * @param text Tekst do skrócenia
 * @param maxLength Maksymalna długość tekstu
 * @returns Skrócony tekst
 */
export function truncateText(text: string, maxLength: number) {
  if (!text) return "";
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + "...";
}
