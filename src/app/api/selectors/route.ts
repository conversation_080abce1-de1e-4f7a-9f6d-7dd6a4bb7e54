import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET() {
  try {
    // Pobierz selektory z bazy danych
    const selectors = await prisma.selector.findMany({
      orderBy: { name: 'asc' }
    });
    
    if (selectors.length === 0) {
      // Jeśli nie ma selektorów, utwórz domyślne
      const defaultSelectors = [
        {
          name: "brandModel",
          cssSelector: "section div p:first-child a",
          xpathSelector: "//section//div//p[1]//a",
          description: "Marka i model pojazdu",
          isActive: true
        },
        {
          name: "desc",
          cssSelector: "section div p:nth-child(2)",
          xpathSelector: "//section//div//p[2]",
          description: "Opis ogłoszenia",
          isActive: true
        },
        {
          name: "date",
          cssSelector: "section div dl dd:nth-child(2) p",
          xpathSelector: "//section//div//dl//dd[2]//p",
          description: "Data dodania ogłoszenia",
          isActive: true
        },
        {
          name: "location",
          cssSelector: "section div dl dd:nth-child(1) p",
          xpathSelector: "//section//div//dl//dd[1]//p",
          description: "Lokalizacja",
          isActive: true
        },
        {
          name: "mileage",
          cssSelector: "section div dl dd[data-parameter=\"mileage\"]",
          xpathSelector: "//section//div//dl//dd[@data-parameter='mileage']",
          description: "Przebieg pojazdu",
          isActive: true
        },
        {
          name: "year",
          cssSelector: "section div dl dd[data-parameter=\"year\"]",
          xpathSelector: "//section//div//dl//dd[@data-parameter='year']",
          description: "Rok produkcji",
          isActive: true
        },
        {
          name: "price",
          cssSelector: "section div div:nth-child(4) div:nth-child(2) h3",
          xpathSelector: "//section//div//div[4]//div[2]//h3",
          description: "Cena pojazdu",
          isActive: true
        },
        {
          name: "url",
          cssSelector: "section div h2 a",
          xpathSelector: "//section//div//h2//a",
          description: "URL ogłoszenia",
          isActive: true
        },
        {
          name: "adContainer",
          cssSelector: "[data-testid=\"search-results\"] > div > article",
          xpathSelector: "//*[@data-testid='search-results']/div/article",
          description: "Kontener ogłoszenia",
          isActive: true
        },
        {
          name: "cookieConsent",
          cssSelector: "button[data-role='accept-consent']",
          xpathSelector: "//button[@data-role='accept-consent']",
          description: "Przycisk akceptacji cookies",
          isActive: true
        },
        {
          name: "nextPage",
          cssSelector: "a[data-testid='pagination-step-forwards']",
          xpathSelector: "//a[@data-testid='pagination-step-forwards']",
          description: "Przycisk następnej strony",
          isActive: true
        }
      ];
      
      // Użyj transakcji, aby zapewnić atomowość operacji
      await prisma.$transaction(async (tx) => {
        for (const selector of defaultSelectors) {
          // Sprawdź, czy selektor o tej nazwie już istnieje
          const exists = await tx.selector.findFirst({
            where: { name: selector.name }
          });
          
          if (!exists) {
            await tx.selector.create({
              data: selector
            });
          }
        }
      });
      
      return NextResponse.json(await prisma.selector.findMany({
        orderBy: { name: 'asc' }
      }));
    }
    
    return NextResponse.json(selectors);
  } catch (error) {
    console.error("Błąd podczas pobierania selektorów:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania selektorów" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Przygotowanie danych selektora do aktualizacji/utworzenia
    const selectorData = {
      name: data.name,
      cssSelector: data.cssSelector || '',
      xpathSelector: data.xpathSelector || '',
      description: data.description,
      isActive: data.isActive !== undefined ? data.isActive : true,
    };
    
    if (data.id) {
      // Aktualizuj istniejący selektor
      const updatedSelector = await prisma.selector.update({
        where: { id: data.id },
        data: selectorData
      });
      
      return NextResponse.json(updatedSelector);
    } else {
      // Sprawdź, czy selektor o tej nazwie już istnieje
      const existingSelector = await prisma.selector.findFirst({
        where: { name: data.name }
      });
      
      if (existingSelector) {
        // Zamiast zwracać błąd, aktualizujemy istniejący selektor
        const updatedSelector = await prisma.selector.update({
          where: { id: existingSelector.id },
          data: selectorData
        });
        
        return NextResponse.json(updatedSelector);
      }
      
      // Utwórz nowy selektor
      const newSelector = await prisma.selector.create({
        data: selectorData
      });
      
      return NextResponse.json(newSelector);
    }
  } catch (error) {
    console.error("Błąd podczas aktualizacji selektora:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas aktualizacji selektora" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { error: "Nie podano ID selektora" },
        { status: 400 }
      );
    }
    
    await prisma.selector.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: "Selektor usunięty pomyślnie" });
  } catch (error) {
    console.error("Błąd podczas usuwania selektora:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas usuwania selektora" },
      { status: 500 }
    );
  }
}
