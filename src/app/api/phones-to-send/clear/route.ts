import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST() {
  try {
    // Usuń wszystkie numery z listy do wysyłki
    await prisma.phoneToSend.deleteMany({});
    
    return NextResponse.json({ message: "Lista numerów telefonów do wysyłki została wyczyszczona" });
  } catch (error) {
    console.error("Błąd podczas czyszczenia listy numerów telefonów do wysyłki:", error);
    return NextResponse.json(
      { error: "Wys<PERSON>ą<PERSON>ł błąd podczas czyszczenia listy numerów telefonów do wysyłki" },
      { status: 500 }
    );
  }
}
