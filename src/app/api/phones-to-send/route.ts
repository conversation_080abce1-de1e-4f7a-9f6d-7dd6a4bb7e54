import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "25");
    const search = searchParams.get("search") || "";
    
    // Oblicz offset dla paginacji
    const skip = (page - 1) * limit;
    
    // Przygotuj warunki wyszukiwania
    const where: any = {};
    
    // Dodaj warunek wyszukiwania, jeśli podano
    if (search) {
      where.number = { contains: search };
    }
    
    // Pobierz numery telefonów do wysyłki z paginacją
    const phones = await prisma.phoneToSend.findMany({
      where,
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });
    
    // Pobierz całkowitą liczbę numerów spełniających kryteria
    const total = await prisma.phoneToSend.count({ where });
    
    return NextResponse.json({
      phones,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Błąd podczas pobierania numerów telefonów do wysyłki:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania numerów telefonów do wysyłki" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { number } = await request.json();
    
    if (!number) {
      return NextResponse.json(
        { error: "Numer telefonu jest wymagany" },
        { status: 400 }
      );
    }
    
    // Walidacja numeru telefonu
    const phoneRegex = /^[0-9]{9}$/;
    if (!phoneRegex.test(number)) {
      return NextResponse.json(
        { error: "Nieprawidłowy format numeru telefonu. Wprowadź 9 cyfr bez spacji i znaków specjalnych." },
        { status: 400 }
      );
    }
    
    // Sprawdź, czy numer jest już na liście
    const existingPhone = await prisma.phoneToSend.findFirst({
      where: { number }
    });
    
    if (existingPhone) {
      return NextResponse.json(
        { error: "Ten numer telefonu jest już na liście do wysyłki" },
        { status: 400 }
      );
    }
    
    // Sprawdź, czy numer jest na liście zablokowanych
    const blockedPhone = await prisma.blockedPhone.findFirst({
      where: { number }
    });
    
    if (blockedPhone) {
      return NextResponse.json(
        { error: "Ten numer telefonu jest na liście zablokowanych" },
        { status: 400 }
      );
    }
    
    // Dodaj numer do listy
    const newPhone = await prisma.phoneToSend.create({
      data: { number }
    });
    
    return NextResponse.json(newPhone);
  } catch (error) {
    console.error("Błąd podczas dodawania numeru telefonu do wysyłki:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas dodawania numeru telefonu do wysyłki" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const id = request.nextUrl.searchParams.get("id");
    
    if (!id) {
      return NextResponse.json(
        { error: "ID numeru telefonu jest wymagane" },
        { status: 400 }
      );
    }
    
    // Usuń numer z listy
    await prisma.phoneToSend.delete({
      where: { id: parseInt(id) }
    });
    
    return NextResponse.json({ message: "Numer telefonu został usunięty z listy do wysyłki" });
  } catch (error) {
    console.error("Błąd podczas usuwania numeru telefonu z listy do wysyłki:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas usuwania numeru telefonu z listy do wysyłki" },
      { status: 500 }
    );
  }
}
