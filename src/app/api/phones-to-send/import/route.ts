import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import fs from "fs";
import path from "path";
import os from "os";

const prisma = new PrismaClient();

// Funkcja do logowania
async function logAction(level: 'info' | 'warn' | 'error', message: string): Promise<void> {
  try {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      source: 'Import numerów',
      message
    };

    // Zapisz log do konsoli
    console[level](`[${timestamp}] [Import numerów] ${message}`);

    // Ścieżka do pliku logów
    const logDir = path.join(process.cwd(), 'logs');
    const logFilePath = path.join(logDir, 'scraper.log');

    // Upewnij się, że katalog istnieje
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Zapisz log do pliku
    fs.appendFileSync(
      logFilePath,
      JSON.stringify(logEntry) + os.EOL,
      'utf-8'
    );
  } catch (error) {
    console.error('Błąd podczas logowania:', error);
  }
}

/**
 * Normalizuje numer telefonu (usuwa spacje, myślniki, nawiasy, prefiks +48/48)
 * @param {string} phone - Numer telefonu do normalizacji
 * @returns {string} - Znormalizowany numer telefonu (zawsze 9-cyfrowy)
 */
function normalizePhoneNumber(phone: string): string {
  if (!phone) return '';
  
  // Usuń wszystkie nie-cyfry
  let cleaned = phone.replace(/\D/g, '');
  
  // Usuń prefiks +48 lub 48 (jeśli jest)
  if (cleaned.startsWith('48')) {
    cleaned = cleaned.substring(2);
  }
  
  return cleaned;
}

/**
 * Wczytuje numery telefonów z tekstu w formacie TXT
 */
function loadPhoneNumbersFromTxt(content: string): string[] {
  return content
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0 && !isNaN(Number(line.replace(/\D/g, ''))));
}

/**
 * Wczytuje numery telefonów z tekstu w formacie CSV
 */
function loadPhoneNumbersFromCsv(content: string): string[] {
  return content
    .split('\n')
    .map(line => {
      // Obsługa pól w cudzysłowach (np. "48123456789")
      const match = line.match(/"([^"]+)"/) || line.match(/([^,]+)/);
      return match ? match[1].trim() : '';
    })
    .filter(line => line.length > 0 && !isNaN(Number(line.replace(/\D/g, ''))));
}

/**
 * Wczytuje numery telefonów z tekstu w formacie JSON
 */
function loadPhoneNumbersFromJson(content: string): string[] {
  try {
    const jsonData = JSON.parse(content);
    
    // Obsługa różnych formatów JSON
    if (Array.isArray(jsonData)) {
      // Jeśli JSON to tablica, spróbuj pobrać numery telefonów
      if (typeof jsonData[0] === 'string') {
        // Format: ["123456789", "987654321"]
        return jsonData;
      } else if (typeof jsonData[0] === 'object') {
        // Format: [{"number": "123456789"}, {"number": "987654321"}]
        // lub [{"phone": "123456789"}, {"phone": "987654321"}]
        return jsonData.map((item: any) => item.number || item.phone || '').filter((number: string) => number);
      }
    } else if (typeof jsonData === 'object') {
      // Format: {"numbers": ["123456789", "987654321"]}
      // lub {"phones": ["123456789", "987654321"]}
      if (Array.isArray(jsonData.numbers)) {
        return jsonData.numbers;
      } else if (Array.isArray(jsonData.phones)) {
        return jsonData.phones;
      }
    }
    
    throw new Error('Nieprawidłowy format JSON');
  } catch (error) {
    throw new Error(`Błąd podczas parsowania JSON: ${error instanceof Error ? error.message : 'Nieznany błąd'}`);
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const fileFormat = formData.get('format') as string;
    const file = formData.get('file') as File;
    
    if (!file || !fileFormat) {
      return NextResponse.json(
        { error: "Brak pliku lub formatu" },
        { status: 400 }
      );
    }
    
    // Sprawdź, czy format jest obsługiwany
    if (fileFormat !== 'txt' && fileFormat !== 'json' && fileFormat !== 'csv') {
      return NextResponse.json(
        { error: "Nieobsługiwany format pliku. Obsługiwane formaty: txt, json, csv" },
        { status: 400 }
      );
    }
    
    // Odczytaj zawartość pliku
    const fileContent = await file.text();
    
    // Wczytaj numery telefonów w zależności od formatu
    let phoneNumbers: string[] = [];
    try {
      if (fileFormat === 'txt') {
        phoneNumbers = loadPhoneNumbersFromTxt(fileContent);
      } else if (fileFormat === 'json') {
        phoneNumbers = loadPhoneNumbersFromJson(fileContent);
      } else if (fileFormat === 'csv') {
        phoneNumbers = loadPhoneNumbersFromCsv(fileContent);
      }
    } catch (error) {
      await logAction('error', `Błąd podczas przetwarzania pliku: ${error instanceof Error ? error.message : 'Nieznany błąd'}`);
      return NextResponse.json(
        { error: `Błąd podczas przetwarzania pliku: ${error instanceof Error ? error.message : 'Nieznany błąd'}` },
        { status: 400 }
      );
    }
    
    if (phoneNumbers.length === 0) {
      return NextResponse.json(
        { error: "Nie znaleziono numerów telefonów w pliku" },
        { status: 400 }
      );
    }
    
    // Normalizacja numerów telefonów (usuń spacje, myślniki, itd.)
    const normalizedPhoneNumbers = phoneNumbers.map(normalizePhoneNumber).filter(Boolean);
    
    if (normalizedPhoneNumbers.length === 0) {
      return NextResponse.json(
        { error: "Nie znaleziono poprawnych numerów telefonów w pliku po normalizacji" },
        { status: 400 }
      );
    }
    
    await logAction('info', `Rozpoczęto import ${normalizedPhoneNumbers.length} numerów telefonów`);
    
    // Znajdź ogłoszenia powiązane z tymi numerami
    const phones = await prisma.phone.findMany({
      where: {
        number: {
          in: normalizedPhoneNumbers
        }
      },
      select: {
        adId: true
      }
    });
    
    // Aktualizuj ogłoszenia - oznacz jako "SMS wysłany"
    // Używamy Set, aby mieć unikalne ID ogłoszeń
    const adIds = [...new Set(phones.map(phone => phone.adId))];
    
    let updatedAds = 0;
    if (adIds.length > 0) {
      const now = new Date();
      
      // Aktualizuj tylko ogłoszenia, które nie są jeszcze oznaczone jako SMS wysłany
      const updateResult = await prisma.ad.updateMany({
        where: {
          id: {
            in: adIds
          },
          smsSent: false // Tylko te, które jeszcze nie mają oznaczonego wysłania
        },
        data: {
          smsSent: true,
          smsSentAt: now
        }
      });
      
      updatedAds = updateResult.count;
      await logAction('info', `Zaktualizowano ${updatedAds} ogłoszeń (oznaczono jako SMS wysłany)`);
    }
    
    // Dodaj wpisy do historii wysłanych SMS-ów (aby były w karencji)
    const now = new Date();
    const smsHistoryEntries = normalizedPhoneNumbers.map(number => ({
      number,
      sentAt: now
    }));
    
    // Dodaj wpisy do historii wysłanych SMS-ów, unikając duplikatów
    // Zamiast używać skipDuplicates (które może nie być obsługiwane w tej wersji Prisma),
    // ręcznie filtrujemy już istniejące numery
    const existingHistory = await prisma.sentSmsHistory.findMany({
      where: {
        number: {
          in: normalizedPhoneNumbers
        }
      },
      select: {
        number: true
      }
    });
    
    const existingNumbers = new Set(existingHistory.map(h => h.number));
    const newEntries = smsHistoryEntries.filter(entry => !existingNumbers.has(entry.number));
    
    if (newEntries.length > 0) {
      await prisma.sentSmsHistory.createMany({
        data: newEntries
      });
    }
    
    // Usuń numery z listy do wysłania
    const deletedPhones = await prisma.phoneToSend.deleteMany({
      where: {
        number: {
          in: normalizedPhoneNumbers
        }
      }
    });
    
    // Podsumowanie operacji
    const summary = {
      imported: normalizedPhoneNumbers.length,
      phonesFound: phones.length,
      adsUpdated: updatedAds,
      smsHistoryAdded: normalizedPhoneNumbers.length,
      phonesToSendDeleted: deletedPhones.count
    };
    
    await logAction('info', `Import zakończony. Podsumowanie: ${JSON.stringify(summary)}`);
    
    return NextResponse.json({
      success: true,
      message: `Zaimportowano ${normalizedPhoneNumbers.length} numerów telefonów`,
      summary
    });
  } catch (error) {
    console.error("Błąd podczas importu numerów telefonów:", error);
    await logAction('error', `Błąd podczas importu numerów telefonów: ${error instanceof Error ? error.message : 'Nieznany błąd'}`);
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : "Wystąpił błąd podczas importu numerów telefonów" 
      },
      { status: 500 }
    );
  }
}
