import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function DELETE() {
  try {
    const prisma = new PrismaClient();
    
    const result = await prisma.phone.deleteMany({});
    await prisma.$disconnect();
    
    return NextResponse.json({ 
      success: true, 
      count: result.count,
      message: `Usunięto wszystkie numery telefonów (${result.count})` 
    });
  } catch (error) {
    console.error('Błąd podczas usuwania numerów telefonów:', error);
    return NextResponse.json(
      { success: false, error: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas usuwania numerów telefonów' },
      { status: 500 }
    );
  }
}