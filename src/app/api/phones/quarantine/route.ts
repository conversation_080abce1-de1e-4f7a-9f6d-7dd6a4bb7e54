import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyToken } from "@/lib/auth/jwt";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Weryfikacja tokenu JWT
    const token = request.cookies.get("token")?.value;
    if (!token) {
      return new NextResponse(
        JSON.stringify({ error: "Brak autoryzacji" }),
        { status: 401 }
      );
    }

    const decodedToken = await verifyToken(token);
    if (!decodedToken) {
      return new NextResponse(
        JSON.stringify({ error: "Nieprawidłowy token" }),
        { status: 401 }
      );
    }

    // Pobierz parametry z URL
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "25");
    const search = searchParams.get("search") || "";
    
    // Oblicz offset dla paginacji
    const offset = (page - 1) * limit;
    
    // Pobierz ustawienia karencji
    const settings = await prisma.settings.findFirst();
    const quarantineDays = settings?.smsResendDays || 90;
    
    // Oblicz datę graniczną dla karencji
    const quarantineDate = new Date();
    quarantineDate.setDate(quarantineDate.getDate() - quarantineDays);
    
    // Filtrowanie po numerze telefonu, jeśli podano search
    const where = search
      ? { number: { contains: search } }
      : {};
    
    // Pobierz numery w karencji z bazy danych
    const [sentSmsHistory, total] = await Promise.all([
      prisma.sentSmsHistory.findMany({
        where,
        orderBy: { sentAt: "desc" },
        skip: offset,
        take: limit,
      }),
      prisma.sentSmsHistory.count({ where }),
    ]);
    
    // Przekształć dane na format odpowiedzi
    const currentDate = new Date();
    const phones = sentSmsHistory.map((sms) => {
      // Oblicz datę wygaśnięcia karencji
      const expiresAt = new Date(sms.sentAt);
      expiresAt.setDate(expiresAt.getDate() + quarantineDays);
      
      // Oblicz pozostałą liczbę dni
      const timeDiff = expiresAt.getTime() - currentDate.getTime();
      const daysLeft = Math.ceil(timeDiff / (1000 * 3600 * 24));
      
      return {
        id: sms.id,
        number: sms.number,
        sentAt: sms.sentAt.toISOString(),
        expiresAt: expiresAt.toISOString(),
        daysLeft: daysLeft
      };
    });
    
    // Oblicz liczbę stron
    const totalPages = Math.ceil(total / limit);
    
    // Przygotuj paginację
    const pagination = {
      page,
      limit,
      total,
      totalPages,
    };
    
    return NextResponse.json({ phones, pagination });
  } catch (error) {
    console.error("Błąd podczas pobierania numerów w karencji:", error);
    return new NextResponse(
      JSON.stringify({ error: "Wystąpił błąd podczas pobierania numerów w karencji" }),
      { status: 500 }
    );
  }
}
