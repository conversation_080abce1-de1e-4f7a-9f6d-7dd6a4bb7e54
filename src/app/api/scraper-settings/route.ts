import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET() {
  try {
    // Pobierz ustawienia scrapera z bazy danych
    const scraperSettings = await prisma.scraperSettings.findFirst();
    
    if (!scraperSettings) {
    // Jeśli nie ma ustawień, utwórz domyślne
    const defaultData = {
      scraperType: "puppeteer",
      headless: false,
      slowMo: 100,
      userAgentRotation: true,
      proxyEnabled: false,
      maxPages: -1, // Domyślnie scrapuj wszystkie strony
      turboMode: false, // Domyślnie wyłączony tryb turbo
      allowedRegions: "[\"Mazowieckie\",\"Łódzkie\",\"Lubelskie\",\"Świętokrzyskie\",\"Podlaskie\"]" // Domyślne województwa
    };
    
    const defaultSettings = await prisma.scraperSettings.create({
      data: defaultData as any
    });
      
      return NextResponse.json(defaultSettings);
    }
    
    return NextResponse.json(scraperSettings);
  } catch (error) {
    console.error("Błąd podczas pobierania ustawień scrapera:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania ustawień scrapera" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log("Otrzymane dane:", data);
    
    // Pobierz istniejące ustawienia
    const existingSettings = await prisma.scraperSettings.findFirst();
    
    if (existingSettings) {
      console.log("Aktualizacja istniejących ustawień:", existingSettings.id);
      
      // Przygotuj dane do aktualizacji z kastowaniem na any dla obejścia problemu TypeScript
      const updateData = {
        scraperType: data.scraperType,
        headless: data.headless,
        slowMo: data.slowMo,
        userAgentRotation: data.userAgentRotation,
        proxyEnabled: data.proxyEnabled,
        proxyUrl: data.proxyUrl,
        maxPages: data.maxPages,
        turboMode: data.turboMode,
        allowedRegions: data.allowedRegions
      };
      
      // Aktualizuj istniejące ustawienia
      const updatedSettings = await prisma.scraperSettings.update({
        where: { 
          id: existingSettings.id 
        },
        data: updateData as any
      });
      
      console.log("Ustawienia zaktualizowane:", updatedSettings);
      return NextResponse.json(updatedSettings);
    } else {
      console.log("Tworzenie nowych ustawień");
      
      // Przygotuj dane do utworzenia nowych ustawień
      const createData = {
        scraperType: data.scraperType || "puppeteer",
        headless: data.headless === undefined ? false : data.headless,
        slowMo: data.slowMo || 100,
        userAgentRotation: data.userAgentRotation === undefined ? true : data.userAgentRotation,
        proxyEnabled: data.proxyEnabled === undefined ? false : data.proxyEnabled,
        proxyUrl: data.proxyUrl || null,
        maxPages: data.maxPages === undefined ? -1 : data.maxPages,
        turboMode: data.turboMode === undefined ? false : data.turboMode,
        allowedRegions: data.allowedRegions || "[\"Mazowieckie\",\"Łódzkie\",\"Lubelskie\",\"Świętokrzyskie\",\"Podlaskie\"]"
      };
      
      // Utwórz nowe ustawienia
      const newSettings = await prisma.scraperSettings.create({
        data: createData as any
      });
      
      console.log("Nowe ustawienia utworzone:", newSettings);
      return NextResponse.json(newSettings);
    }
  } catch (error) {
    console.error("Błąd podczas aktualizacji ustawień scrapera:", error);
    return NextResponse.json(
      { error: `Wystąpił błąd podczas aktualizacji ustawień scrapera: ${error}` },
      { status: 500 }
    );
  }
}
