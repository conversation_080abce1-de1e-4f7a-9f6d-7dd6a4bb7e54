import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";
    const smsSent = searchParams.get("smsSent");

    // Oblicz offset dla paginacji
    const skip = (page - 1) * limit;

    // Przygotuj warunki wyszukiwania
    const where: any = {};

    // Filtr dla ogłoszeń bez numeru telefonu
    const noPhone = searchParams.get("noPhone");
    if (noPhone === "true") {
      where.phones = {
        none: {}
      };
    }
    
    // Dodaj warunek wyszukiwania, jeśli podano
    if (search) {
      // Usuń spacje z zapytania wyszukiwania dla niektórych pól
      const cleanSearch = search.replace(/\s+/g, "");
      const searchLower = search.toLowerCase();
      
      where.OR = [
        { brandModel: { contains: searchLower } },
        { desc: { contains: searchLower } },
        { comment: { contains: searchLower } }, // Dodane wyszukiwanie po komentarzu
        { location: { contains: searchLower } },
        { externalId: { contains: searchLower } },
        // Dodaj wyszukiwanie po numerze telefonu (bez spacji)
        { 
          phones: { 
            some: { 
              number: { 
                contains: cleanSearch
              } 
            } 
          } 
        },
      ];
      
      // Dodaj wyszukiwanie po cenie i przebiegu (zarówno z, jak i bez spacji)
      if (/^\d+$/.test(cleanSearch)) {
        where.OR.push(
          { price: { contains: cleanSearch } },
          { mileage: { contains: cleanSearch } }
        );
        
        // Dla ceny także sprawdź wariant z separatorem tysięcy
        // np. wyszukiwanie "10000" powinno znaleźć także "10 000"
        if (cleanSearch.length > 3) {
          const formatted = cleanSearch.replace(/\B(?=(\d{3})+(?!\d))/g, " ");
          where.OR.push(
            { price: { contains: formatted } }
          );
        }
      }
    }

    // Dodaj filtr statusu SMS, jeśli podano
    if (smsSent === "true") {
      where.smsSent = true;
    } else if (smsSent === "false") {
      where.smsSent = false;
    }

    // Przygotuj sortowanie
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // Pobierz ogłoszenia z paginacją i sortowaniem
    const ads = await prisma.ad.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        phones: true,
      },
    });

    // Pobierz całkowitą liczbę ogłoszeń spełniających kryteria
    const total = await prisma.ad.count({ where });

    return NextResponse.json({
      ads,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Błąd podczas pobierania ogłoszeń:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania ogłoszeń" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, comment } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: "ID ogłoszenia jest wymagane" },
        { status: 400 }
      );
    }

    // Aktualizuj komentarz ogłoszenia
    const updatedAd = await prisma.ad.update({
      where: { id },
      data: { 
        comment: comment as string 
      } as any,
    });

    return NextResponse.json({
      message: "Komentarz został zaktualizowany pomyślnie",
      ad: updatedAd,
    });
  } catch (error) {
    console.error("Błąd podczas aktualizacji komentarza:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas aktualizacji komentarza" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (id) {
      // Usuń konkretne ogłoszenie
      // Numery telefonów zostaną usunięte automatycznie dzięki relacji onDelete: Cascade
      await prisma.ad.delete({
        where: { id },
      });

      return NextResponse.json({ message: "Ogłoszenie usunięte pomyślnie" });
    } else {
      // Usuń wszystkie ogłoszenia (czyszczenie bazy)
      // Numery telefonów zostaną usunięte automatycznie dzięki relacji onDelete: Cascade
      await prisma.ad.deleteMany({});

      return NextResponse.json({ message: "Baza ogłoszeń wyczyszczona pomyślnie" });
    }
  } catch (error) {
    console.error("Błąd podczas usuwania ogłoszeń:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas usuwania ogłoszeń" },
      { status: 500 }
    );
  }
}
