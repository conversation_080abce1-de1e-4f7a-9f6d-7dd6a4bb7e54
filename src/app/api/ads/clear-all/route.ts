import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

export async function DELETE() {
  try {
    const prisma = new PrismaClient();
    
    const result = await prisma.ad.deleteMany({});
    await prisma.$disconnect();
    
    return NextResponse.json({ 
      success: true, 
      count: result.count,
      message: `Usunięto wszystkie ogłoszenia (${result.count})` 
    });
  } catch (error) {
    console.error('Błąd podczas usuwania ogłoszeń:', error);
    return NextResponse.json(
      { success: false, error: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas usuwania ogłoszeń' },
      { status: 500 }
    );
  }
}