import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import nodemailer from "nodemailer";

const prisma = new PrismaClient();

// Konfiguracja transportera Nodemailer
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: "<EMAIL>",
    pass: "qllg rkls mfbj fzqr", // Hasło do aplikacji Google
  },
});

// GET - Sprawdź przypomnienia o leasingu na dzisiaj i wyślij email
export async function GET(request: NextRequest) {
  try {
    // Pobierz dzisiejszą datę
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Pobierz przypomnienia o leasingu na dzisiaj
    const todayReminders = await prisma.leaseReminder.findMany({
      where: {
        expirationDate: {
          gte: today,
          lt: tomorrow,
        },
      },
    });

    // Je<PERSON><PERSON> nie ma przypomnień na dzisiaj, zwr<PERSON><PERSON> odpowiedź
    if (todayReminders.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Brak przypomnień o leasingu na dzisiaj",
        count: 0,
      });
    }

    // Przygotuj treść emaila
    const emailContent = `
      <h1>Przypomnienia o leasingu na dzisiaj (${today.toLocaleDateString('pl-PL')})</h1>
      <p>Liczba przypomnień: <strong>${todayReminders.length}</strong></p>
      <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
        <thead>
          <tr style="background-color: #f2f2f2;">
            <th>Status</th>
            <th>Imię i nazwisko</th>
            <th>Telefon</th>
            <th>Email</th>
            <th>Samochód</th>
            <th>Notatka</th>
          </tr>
        </thead>
        <tbody>
          ${todayReminders.map((reminder: any) => `
            <tr>
              <td>${reminder.status}</td>
              <td>${reminder.fullName}</td>
              <td>${reminder.phone}</td>
              <td>${reminder.email || '-'}</td>
              <td>${reminder.carDetails}</td>
              <td>${reminder.note || '-'}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      <p>
        <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/przypomnienia">
          Przejdź do listy przypomnień
        </a>
      </p>
    `;

    // Opcje emaila
    const mailOptions = {
      from: "<EMAIL>",
      to: "<EMAIL>",
      subject: `Przypomnienia o leasingu na dzisiaj (${today.toLocaleDateString('pl-PL')}) - ${todayReminders.length}`,
      html: emailContent,
    };

    // Wyślij email
    const info = await transporter.sendMail(mailOptions);

    // Zwróć odpowiedź
    return NextResponse.json({
      success: true,
      message: `Email z przypomnieniami został wysłany (${todayReminders.length})`,
      count: todayReminders.length,
      messageId: info.messageId,
    });
  } catch (error) {
    console.error("Błąd podczas wysyłania emaila z przypomnieniami:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Wystąpił błąd podczas wysyłania emaila z przypomnieniami",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
