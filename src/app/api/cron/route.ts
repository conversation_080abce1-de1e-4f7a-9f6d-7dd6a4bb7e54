import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Pobierz status crona - wyłączono logowanie dla tej metody
export async function GET() {
  try {
    // Sprawdź, czy funkcje crona są dostępne
    if (!global.cronFunctions) {
      return NextResponse.json(
        { 
          error: "Funkcje crona nie są dostępne. Serwer musi być uruchomiony z plikiem server.js.",
          status: "unavailable"
        },
        { status: 503 }
      );
    }

    // Pobierz status crona - bez logowania
    const status = await global.cronFunctions.getCronStatus();
    
    return NextResponse.json(status);
  } catch (error) {
    // Usunięto logowanie błędu
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania statusu crona" },
      { status: 500 }
    );
  }
}

// Aktualizuj status crona
export async function POST(request: NextRequest) {
  try {
    const { enabled, interval, runNow, cleanupSmsHistory, stopScraping, sendLeaseReminders } = await request.json();
    
    // Sprawdź, czy funkcje crona są dostępne
    if (!global.cronFunctions) {
      return NextResponse.json(
        { 
          error: "Funkcje crona nie są dostępne. Serwer musi być uruchomiony z plikiem server.js.",
          status: "unavailable"
        },
        { status: 503 }
      );
    }
    
    // Aktualizuj ustawienia w bazie danych
    if (enabled !== undefined || interval !== undefined) {
      const updateData: any = {};
      
      if (enabled !== undefined) {
        updateData.cronEnabled = enabled;
      }
      
      if (interval !== undefined) {
        updateData.scrapingInterval = interval;
      }
      
      await prisma.settings.updateMany({
        data: updateData
      });
      
      // Zainicjuj cron ponownie z nowymi ustawieniami
      await global.cronFunctions.initCron();
    }
    
    // Zatrzymaj scraper, jeśli żądano
    if (stopScraping) {
      const success = await global.cronFunctions.stopScraper();
      if (!success) {
        return NextResponse.json(
          { error: "Nie udało się zatrzymać scrapera" },
          { status: 500 }
        );
      }
    }
    
    // Uruchom scraper natychmiast, jeśli żądano
    if (runNow) {
      global.cronFunctions.runScraper();
    }
    
    // Pobierz aktualny status crona
    const status = await global.cronFunctions.getCronStatus();
    
    // Uruchom czyszczenie historii SMS-ów, jeśli żądano
    if (cleanupSmsHistory) {
      const deletedCount = await global.cronFunctions.runCleanupSentSmsHistory();
      // Dodaj informację o liczbie usuniętych wpisów do odpowiedzi
      (status as any).deletedSmsHistoryCount = deletedCount;
    }
    
    // Wyślij email z przypomnieniami o leasingu, jeśli żądano
    if (sendLeaseReminders) {
      const result = await global.cronFunctions.runSendLeaseRemindersEmail();
      // Dodaj informację o wyniku wysyłania emaila do odpowiedzi
      (status as any).leaseRemindersEmailResult = result;
    }
    
    return NextResponse.json({
      message: "Status crona zaktualizowany pomyślnie",
      status
    });
  } catch (error) {
    console.error("Błąd podczas aktualizacji statusu crona:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas aktualizacji statusu crona" },
      { status: 500 }
    );
  }
}
