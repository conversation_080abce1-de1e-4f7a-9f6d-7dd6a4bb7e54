import { NextResponse } from "next/server";
import { smsApiService } from "@/lib/smsapi/service";

export async function GET() {
  try {
    const status = await smsApiService.pingSmsApi();
    
    return NextResponse.json(status);
  } catch (error) {
    console.error("Błąd podczas sprawdzania statusu bramki SMS:", error);
    return NextResponse.json(
      { 
        status: "error",
        message: error instanceof Error ? error.message : "Wystąpił błąd podczas sprawdzania statusu bramki SMS"
      },
      { status: 500 }
    );
  }
}
