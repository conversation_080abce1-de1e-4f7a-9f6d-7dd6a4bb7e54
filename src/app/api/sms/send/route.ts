import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { smsApiService } from "@/lib/smsapi/service";
import fs from 'fs';
import path from 'path';
import os from 'os';

const prisma = new PrismaClient();

// Funkcja do logowania akcji SMS
async function logSmsAction(level: 'info' | 'warn' | 'error', message: string): Promise<void> {
  try {
    // Format logu
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      source: 'SMS',
      message
    };

    // Zapisz log do konsoli
    console[level](`[${timestamp}] [SMS] ${message}`);

    // Ścieżka do pliku logów
    const logDir = path.join(process.cwd(), 'logs');
    const logFilePath = path.join(logDir, 'scraper.log');

    // Upewnij się, że katalog istnieje
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Zapisz log do pliku
    fs.appendFileSync(
      logFilePath,
      JSON.stringify(logEntry) + os.EOL,
      'utf-8'
    );
  } catch (error) {
    console.error('Błąd podczas logowania:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { adId, phoneId, customContent, sendAll, batchSize = 100, confirmOutsideHours = false } = await request.json();
    
    // Sprawdź czy wysyłka SMS-ów odbywa się w dozwolonych godzinach (8-20)
    const now = new Date();
    const hour = now.getHours();
    const isBusinessHours = hour >= 8 && hour < 20;
    let outsideBusinessHoursWarning = null;
    
    console.log(`Sprawdzanie godziny wysyłki SMS: ${hour}:${now.getMinutes().toString().padStart(2, '0')}`);
    
    if (!isBusinessHours) {
      outsideBusinessHoursWarning = `UWAGA: Wysyłka SMS-ów poza godzinami roboczymi (8-20). Aktualna godzina: ${hour}:${now.getMinutes().toString().padStart(2, '0')}`;
      console.warn(outsideBusinessHoursWarning); // Dodatkowy log w konsoli
      await logSmsAction('warn', outsideBusinessHoursWarning);
      
      // Jeśli wysyłka jest poza godzinami roboczymi i nie ma potwierdzenia, zwróć żądanie potwierdzenia
      if (!confirmOutsideHours) {
        return NextResponse.json({
          requiresConfirmation: true,
          warning: outsideBusinessHoursWarning,
          message: "Wysyłka SMS-ów poza godzinami roboczymi wymaga potwierdzenia."
        }, { status: 200 });
      }
    }
    
    if (!adId && !phoneId && !sendAll) {
      return NextResponse.json(
        { error: "Wymagane jest ID ogłoszenia, ID numeru telefonu lub flaga wysyłania do wszystkich" },
        { status: 400 }
      );
    }
    
    // Pobierz ustawienia aplikacji
    const appSettings = await prisma.settings.findFirst();
    
    if (!appSettings) {
      return NextResponse.json(
        { error: "Nie znaleziono ustawień aplikacji" },
        { status: 404 }
      );
    }
    
    // Pobierz treść SMS-a
    const smsContent = customContent || appSettings.smsContent;
    
    if (!smsContent) {
      return NextResponse.json(
        { error: "Brak treści SMS-a" },
        { status: 400 }
      );
    }
    
    let phoneNumbers: string[] = [];
    let adData: any = null;
    let sentCount = 0;
    
    // Pobierz numer telefonu lub numery telefonów
    if (sendAll) {
      // Pobierz ograniczoną liczbę najstarszych numerów z listy
      const allPhones = await prisma.phoneToSend.findMany({
        orderBy: { createdAt: 'asc' }, // Sortuj po dacie utworzenia, najstarsze pierwsze
        take: batchSize // Ograniczenie liczby numerów do batchSize
      });
      
      await logSmsAction('info', `Pobrano ${allPhones.length} najstarszych numerów telefonów z limitem ${batchSize}`);
      
      if (!allPhones || allPhones.length === 0) {
        return NextResponse.json(
          { error: "Brak numerów telefonów na liście do wysyłki" },
          { status: 404 }
        );
      }
      
      // Filtruj zablokowane numery
      const blockedPhones = await prisma.blockedPhone.findMany();
      const blockedPhoneNumbers = blockedPhones.map(phone => phone.number);
      
      // Filtruj numery, które nie są zablokowane
      phoneNumbers = allPhones
        .map(phone => phone.number)
        .filter(number => !blockedPhoneNumbers.includes(number));
      
      if (phoneNumbers.length === 0) {
        return NextResponse.json(
          { error: "Wszystkie numery na liście są zablokowane" },
          { status: 400 }
        );
      }
    } else if (phoneId) {
      const phone = await prisma.phoneToSend.findUnique({
        where: { id: phoneId }
      });
      
      if (!phone) {
        return NextResponse.json(
          { error: "Nie znaleziono numeru telefonu" },
          { status: 404 }
        );
      }
      
      // Sprawdź, czy numer jest na liście zablokowanych
      const blockedPhone = await prisma.blockedPhone.findFirst({
        where: { number: phone.number }
      });
      
      if (blockedPhone) {
        return NextResponse.json(
          { error: "Ten numer telefonu jest na liście zablokowanych" },
          { status: 400 }
        );
      }
      
      phoneNumbers = [phone.number];
    } else if (adId) {
      // Pobierz ogłoszenie
      adData = await prisma.ad.findUnique({
        where: { id: adId },
        include: { phones: true }
      });
      
      if (!adData) {
        return NextResponse.json(
          { error: "Nie znaleziono ogłoszenia" },
          { status: 404 }
        );
      }
      
      if (!adData.phones || adData.phones.length === 0) {
        return NextResponse.json(
          { error: "Ogłoszenie nie ma przypisanych numerów telefonu" },
          { status: 400 }
        );
      }
      
      // Sprawdź, czy numer jest na liście zablokowanych
      const phoneNumber = adData.phones[0].number;
      const blockedPhone = await prisma.blockedPhone.findFirst({
        where: { number: phoneNumber }
      });
      
      if (blockedPhone) {
        return NextResponse.json(
          { error: "Ten numer telefonu jest na liście zablokowanych" },
          { status: 400 }
        );
      }
      
      phoneNumbers = [phoneNumber];
    }
    
    if (phoneNumbers.length === 0) {
      return NextResponse.json(
        { error: "Nie znaleziono numerów telefonów do wysyłki" },
        { status: 404 }
      );
    }
    
    // Sprawdź, czy numery nie otrzymały SMS-a w określonym czasie
    const smsResendDays = appSettings.smsResendDays || 90; // Domyślnie 90 dni
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - smsResendDays);
    
    // Filtruj numery, które otrzymały SMS w okresie karencji
    const numbersWithRecentSms = await Promise.all(
      phoneNumbers.map(async (number) => {
        const recentSms = await prisma.sentSmsHistory.findFirst({
          where: {
            number: number,
            sentAt: {
              gte: cutoffDate
            }
          }
        });
        return { number, recentSms };
      })
    );
    
    // Odfiltruj numery, które otrzymały SMS w okresie karencji
    const eligibleNumbers = numbersWithRecentSms
      .filter(item => !item.recentSms)
      .map(item => item.number);
    
    await logSmsAction('info', `Przygotowanie ${eligibleNumbers.length} numerów do wysyłki SMS`);
    
    if (eligibleNumbers.length === 0) {
      return NextResponse.json(
        { error: "Wszystkie numery otrzymały już SMS w okresie karencji" },
        { status: 400 }
      );
    }
    
    // Funkcja dodająca prefiks 48 do numeru telefonu, jeśli go nie ma
  function addPolishPrefix(phone: string): string {
    // Usuń wszystkie nie-cyfry
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Jeśli numer ma 9 cyfr, dodaj prefiks 48
    if (cleanPhone.length === 9) {
      return `48${cleanPhone}`;
    }
    
    // W przeciwnym razie zwróć oryginalny numer
    return cleanPhone;
  }

  // Przygotuj dane do wysyłki
  const shipments = eligibleNumbers.map(number => ({
    phone: addPolishPrefix(number), // Dodaj prefiks 48 przed wysyłką
    content: smsContent
  }));
    
    // Wyślij wszystkie SMS-y jednym żądaniem (bez podziału na partie)
    await logSmsAction('info', `Wysyłanie SMS-ów: ${shipments.length} numerów`);
    
    const result = await smsApiService.createSmsShipment(shipments);
    const results = [result]; // Zachowujemy w tablicy dla kompatybilności z resztą kodu
    
    // Pokaż więcej cyfr z numeru telefonu w logach (ostatnie 9 cyfr)
    const numbersPreview = shipments.map(item => {
      // Weź ostatnie 9 cyfr numeru lub cały numer jeśli jest krótszy
      return item.phone.toString().slice(-9);
    }).join(',').substring(0, 50); // Pozwól na dłuższy string w logach
    const apiResponse = result && result['ns2:SupResponse'];
    const statusInfo = apiResponse ? apiResponse.header?.status || 'BRAK_STATUSU' : 'BRAK_ODPOWIEDZI';
    
    // Sprawdź czy status to FAILED i wyciągnij więcej szczegółów
    let errorDetailsString = '';
    if (statusInfo === 'FAILED' && apiResponse) {
      // Próba pobrania informacji o błędzie
      const errorCode = apiResponse.header?.errorCode || '';
      const errorMessage = apiResponse.header?.errorMessage || '';
      if (errorCode || errorMessage) {
        errorDetailsString = ` | Kod błędu: ${errorCode} | Komunikat: ${errorMessage}`;
      }
      
      // Sprawdź czy istnieją dodatkowe informacje o błędzie
      if (apiResponse.createShipment?.result?.errorDescription) {
        errorDetailsString += ` | Opis: ${apiResponse.createShipment.result.errorDescription}`;
      }
    }
    
    await logSmsAction(statusInfo === 'FAILED' ? 'error' : 'info', 
      `Status wysyłki: ${statusInfo}${errorDetailsString}, numery: ${numbersPreview}${numbersPreview.length < 50 ? '' : '...'}`);
    
    // Zapisz historię wysłanych SMS-ów i usuń numer z listy do wysyłki tylko jeśli wysyłka się powiodła
    if (statusInfo !== 'FAILED') {
      for (let j = 0; j < shipments.length; j++) {
        const originalNumber = eligibleNumbers[j];
        
        // Zapisz historię wysłanych SMS-ów tylko jeśli wysyłka się powiodła
        await prisma.sentSmsHistory.create({
          data: {
            number: originalNumber, // Zapisz oryginalny numer (bez prefiksu) w historii
            sentAt: new Date()
          }
        });
        
        // Usuń numer z listy do wysyłki - używamy oryginalnego numeru (bez prefiksu)
        await prisma.phoneToSend.deleteMany({
          where: { number: originalNumber }
        });
      }
      
      // Zwiększ licznik pomyślnie wysłanych SMS-ów
      sentCount += shipments.length;
    } else {
      // Pobierz pełną odpowiedź
      
      // Zapisz pełną odpowiedź w logu
      await logSmsAction('error', 
        `Nie udało się wysłać ${shipments.length} SMS-ów. Numery pozostaną na liście do wysyłki. ` +
        `Pełna odpowiedź bramki: ${JSON.stringify(apiResponse, null, 2)}`);
    }
    
    // Aktualizuj status SMS-a w ogłoszeniu jeśli to wysyłka dla ogłoszenia
    if (adData) {
      await prisma.ad.update({
        where: { id: adData.id },
        data: {
          smsSent: true,
          smsSentAt: new Date()
        }
      });
    }

    // Zawsze aktualizuj statusy ogłoszeń dla wszystkich wysłanych numerów
    // Niezależnie od tego, czy to wysyłka dla pojedynczego ogłoszenia czy masowa
    if (sentCount > 0) {
      // Zapisz dziennik akcji
      await logSmsAction('info', `Aktualizowanie statusów ogłoszeń dla wysłanych SMS-ów...`);
      
      // Dla każdego numeru, znajdź i zaktualizuj powiązane ogłoszenia
      let updatedAdsCount = 0;
      const successfullySentNumbers = [];
      
      // Zbierz numery, które zostały pomyślnie wysłane
      for (let i = 0; i < Math.min(sentCount, eligibleNumbers.length); i++) {
        successfullySentNumbers.push(eligibleNumbers[i]);
      }
      
      // Znajdź wszystkie ogłoszenia powiązane z tymi numerami na raz
      const phoneRecords = await prisma.phone.findMany({
        where: {
          number: {
            in: successfullySentNumbers
          }
        },
        select: {
          adId: true
        }
      });
      
      // Wyodrębnij unikalne ID ogłoszeń
      const uniqueAdIds = [...new Set(phoneRecords.map(phone => phone.adId))];
      
      // Zaktualizuj wszystkie powiązane ogłoszenia jedną operacją
      if (uniqueAdIds.length > 0) {
        const now = new Date();
        const updateResult = await prisma.ad.updateMany({
          where: {
            id: {
              in: uniqueAdIds
            },
            smsSent: false // Tylko te, które jeszcze nie mają oznaczonego wysłania
          },
          data: {
            smsSent: true,
            smsSentAt: now
          }
        });
        
        updatedAdsCount = updateResult.count;
      }
      
      if (updatedAdsCount > 0) {
        await logSmsAction('info', `Zaktualizowano status ${updatedAdsCount} ogłoszeń`);
      }
    }
    
    // Aktualizuj statystyki
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const stats = await prisma.stats.findFirst({
      where: {
        date: {
          gte: today
        }
      }
    });
    
    if (stats) {
      await prisma.stats.update({
        where: { id: stats.id },
        data: { smsSent: stats.smsSent + 1 }
      });
    } else {
      await prisma.stats.create({
        data: { smsSent: 1 }
      });
    }
    
    // Dodaj wpis w logach o zakończeniu wysyłki
    await logSmsAction('info', `Zakończono wysyłkę SMS-ów. Wysłano pomyślnie ${sentCount} wiadomości.`);
    
    // Przygotuj szczegółowe informacje o błędach, jeśli wystąpiły
    const errorDetails = results
      .filter(result => {
        const respObj = result && result['ns2:SupResponse'];
        return respObj && respObj.header?.status === 'FAILED';
      })
      .map(result => {
        const respObj = result['ns2:SupResponse'];
        const errorCode = respObj.header?.errorCode || '';
        const errorMessage = respObj.header?.errorMessage || '';
        const errorDescription = respObj.createShipment?.result?.errorDescription || '';
        
        return {
          errorCode,
          errorMessage,
          errorDescription
        };
      });
    
    // Sprawdź, czy wystąpiły błędy podczas wysyłki
    const hasErrors = errorDetails.length > 0;
    
    // Przygotuj odpowiedź API
    const apiResult: {
      success: boolean;
      message: string;
      sentCount: number;
      results: any[];
      errorDetails?: any[];
      warning?: string;
      errorMessage?: string;
    } = {
      success: !hasErrors && sentCount > 0,
      message: hasErrors 
        ? `Nie udało się wysłać SMS-ów. Numery pozostaną na liście do wysyłki.` 
        : (phoneNumbers.length > 1 
           ? `SMSy zostały wysłane pomyślnie do ${sentCount} numerów` 
           : "SMS został wysłany pomyślnie"),
      sentCount,
      results,
      errorDetails: errorDetails.length > 0 ? errorDetails : undefined
    };
    
    // Jeśli wystąpiły błędy, dodaj komunikat o błędzie
    if (hasErrors) {
      // Dodaj czytelny komunikat błędu
      apiResult.errorMessage = `Nie udało się wysłać ${shipments.length} SMS-ów. Numery pozostaną na liście do wysyłki.`;
    }
    
    // Dodaj ostrzeżenie o wysyłce poza godzinami roboczymi, jeśli istnieje
    if (outsideBusinessHoursWarning) {
      apiResult.warning = outsideBusinessHoursWarning;
      
      // Modyfikuj główny komunikat, aby zawierał ostrzeżenie
      if (!hasErrors) {
        apiResult.message = `${apiResult.message} (${outsideBusinessHoursWarning})`;
      }
    }
    
    // Zwróć odpowiedź z odpowiednim statusem
    return NextResponse.json(apiResult, { 
      status: hasErrors ? 400 : 200 
    });
  } catch (error) {
    console.error("Błąd podczas wysyłania SMS-a:", error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : "Wystąpił błąd podczas wysyłania SMS-a"
      },
      { status: 500 }
    );
  }
}
