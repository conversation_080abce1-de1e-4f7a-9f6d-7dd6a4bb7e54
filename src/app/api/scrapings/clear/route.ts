import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// DELETE /api/scrapings/clear - usuwanie wszystkich sesji scrapowania
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const keepCurrent = searchParams.get('keepCurrent') === 'true';

    if (keepCurrent) {
      // Zostaw aktywne scrapowanie, jeśli istnieje
      const currentSession = await prisma.scrapingSession.findFirst({
        where: {
          status: 'in_progress'
        },
        orderBy: {
          startedAt: 'desc'
        }
      });

      if (currentSession) {
        // Usuń wszystkie sesje oprócz aktualnie aktywnej
        await prisma.scrapingSession.deleteMany({
          where: {
            id: {
              not: currentSession.id
            }
          }
        });

        return NextResponse.json({
          message: 'Usunięto wszystkie sesje scrapowania oprócz aktualnie aktywnej',
          activeSessionId: currentSession.id
        });
      }
    }

    // Usuń wszystkie sesje
    await prisma.scrapingSession.deleteMany();

    return NextResponse.json({
      message: 'Usunięto wszystkie sesje scrapowania'
    });
  } catch (error) {
    console.error('Błąd podczas usuwania sesji scrapowania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania sesji scrapowania' },
      { status: 500 }
    );
  }
}
