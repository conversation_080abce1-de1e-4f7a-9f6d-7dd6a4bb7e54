import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { updateScrapingStatus } from '@/lib/scrapers/session-utils';

const prisma = new PrismaClient();

// POST /api/scrapings/pause - wstrzymanie aktywnego scrapowania
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID sesji jest wymagane' },
        { status: 400 }
      );
    }
    
    const scrapingSession = await prisma.scrapingSession.findUnique({
      where: { id }
    });
    
    if (!scrapingSession) {
      return NextResponse.json(
        { error: 'Nie znaleziono sesji scrapowania' },
        { status: 404 }
      );
    }
    
    // Sprawdź, czy sesja jest aktywna
    if (scrapingSession.status !== 'in_progress' && 
        scrapingSession.status !== 'collecting_ids' && 
        scrapingSession.status !== 'collecting_details') {
      return NextResponse.json(
        { error: `Nie można wstrzymać sesji o statusie: ${scrapingSession.status}` },
        { status: 400 }
      );
    }
    
    // Aktualizuj status sesji na "paused"
    const pauseDuration = 600; // 10 minut pauzy
    const updatedSession = await prisma.scrapingSession.update({
      where: { id },
      data: {
        status: 'paused',
        pausedAt: new Date(),
        pauseDuration: pauseDuration,
        pauseReason: 'manual_pause' // Wskazujemy, że to ręczna pauza
      }
    });
    
    // Przygotuj odpowiedź
    const response = {
      success: true,
      message: 'Sesja scrapowania została wstrzymana',
      session: updatedSession,
      resumeAt: new Date(new Date().getTime() + pauseDuration * 1000)
    };
    
    // Zatrzymaj faktycznie proces scrapowania
    console.log(`Oznaczono sesję ID=${id} jako wstrzymaną (status='paused')`);
    
    // Sprawdź, czy mamy dostęp do globalnych funkcji cron
    if (!global.cronFunctions) {
      console.error('Brak dostępu do global.cronFunctions');
      return NextResponse.json(
        { 
          error: 'Funkcja zatrzymania scrapowania nie jest dostępna',
          success: false 
        },
        { status: 500 }
      );
    }
    
    // Pobierz i zapisz zescrapowane ID ogłoszeń (jeśli funkcja jest dostępna)
    // @ts-ignore - Ignorujemy błąd TypeScript, ponieważ sprawdzamy dostępność funkcji w czasie wykonania
    if (global.cronFunctions && typeof global.cronFunctions.getCurrentScrapedAdIds === 'function') {
      try {
        // @ts-ignore - Ignorujemy błąd TypeScript, ponieważ sprawdzamy dostępność funkcji w czasie wykonania
        const scrapedAdIds = await global.cronFunctions.getCurrentScrapedAdIds();
        if (scrapedAdIds && scrapedAdIds.length > 0) {
          // Zapisz listę ID do bazy danych
          await prisma.scrapingSession.update({
            where: { id },
            data: {
              scrapedAdIds: JSON.stringify(scrapedAdIds)
            }
          });
          console.log(`Zapisano ${scrapedAdIds.length} ID ogłoszeń dla sesji ID=${id}`);
        }
      } catch (idsError) {
        console.error('Błąd podczas pobierania/zapisywania ID ogłoszeń:', idsError);
      }
    }
    
    // Zatrzymaj aktywny proces scrapowania - zamyka przeglądarkę i zatrzymuje wszystkie procesy
    if (typeof global.cronFunctions.stopScraper === 'function') {
      try {
        const stopResult = await global.cronFunctions.stopScraper();
        console.log(`Wynik zatrzymania procesu scrapowania: ${stopResult ? 'sukces' : 'błąd'}`);
      } catch (stopError) {
        console.error('Błąd podczas zatrzymywania procesu scrapowania:', stopError);
      }
    }
    
    // Zresetuj status scrapowania
    updateScrapingStatus(false);
    console.log(`Zresetowano status scrapowania dla sesji ID=${id}`);
    
    return NextResponse.json(response);
  } catch (error: any) {
    console.error('Błąd podczas próby wstrzymania scrapowania:', error);
    return NextResponse.json(
      { 
        error: 'Wystąpił błąd podczas próby wstrzymania scrapowania',
        details: error.message || String(error)
      },
      { status: 500 }
    );
  }
}
