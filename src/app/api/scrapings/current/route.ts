import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/scrapings/current - pobieranie informacji o aktualnej sesji scrapowania
export async function GET(request: NextRequest) {
  try {
    // Najpierw sprawdź, czy istnieje sesja w trakcie (status: in_progress)
    let scrapingSession = await prisma.scrapingSession.findFirst({
      where: {
        status: 'in_progress'
      },
      orderBy: {
        startedAt: 'desc'
      }
    });

    // Jeśli nie ma aktywnej sesji, pobierz ostatnią zakończoną
    if (!scrapingSession) {
      scrapingSession = await prisma.scrapingSession.findFirst({
        orderBy: {
          startedAt: 'desc'
        }
      });
    }

    if (!scrapingSession) {
      return NextResponse.json({ status: 'no_session' });
    }

    // Oblicz procenty wykonania
    const response = {
      ...scrapingSession,
      pageProgress: scrapingSession.totalPages > 0 
        ? Math.floor((scrapingSession.currentPage / scrapingSession.totalPages) * 100) 
        : 0,
      adsIdsProgress: scrapingSession.totalAdsIds > 0 
        ? Math.floor((scrapingSession.adsIdsScraped / scrapingSession.totalAdsIds) * 100) 
        : 0,
      adsDetailsProgress: scrapingSession.totalAdsIds > 0 
        ? Math.floor((scrapingSession.adsDetailsScraped / scrapingSession.totalAdsIds) * 100) 
        : 0,
      adsWithPhonesProgress: scrapingSession.totalAdsIds > 0 
        ? Math.floor((scrapingSession.adsWithPhones / scrapingSession.totalAdsIds) * 100) 
        : 0
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Błąd podczas pobierania aktualnej sesji scrapowania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania aktualnej sesji scrapowania' },
      { status: 500 }
    );
  }
}

// PUT /api/scrapings/current - aktualizacja bieżącej sesji scrapowania
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      id, 
      currentPage, 
      status, 
      adsIdsScraped, 
      adsDetailsScraped, 
      adsWithPhones, 
      totalAdsIds,
      finishedAt
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID sesji jest wymagane' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    
    if (currentPage !== undefined) updateData.currentPage = currentPage;
    if (status !== undefined) updateData.status = status;
    if (adsIdsScraped !== undefined) updateData.adsIdsScraped = adsIdsScraped;
    if (adsDetailsScraped !== undefined) updateData.adsDetailsScraped = adsDetailsScraped;
    if (adsWithPhones !== undefined) updateData.adsWithPhones = adsWithPhones;
    if (totalAdsIds !== undefined) updateData.totalAdsIds = totalAdsIds;
    
    // Jeśli status jest 'completed' lub 'interrupted', ustaw czas zakończenia
    if (status === 'completed' || status === 'interrupted') {
      updateData.finishedAt = finishedAt || new Date();
    }

    const updatedSession = await prisma.scrapingSession.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json(updatedSession);
  } catch (error) {
    console.error('Błąd podczas aktualizacji sesji scrapowania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas aktualizacji sesji scrapowania' },
      { status: 500 }
    );
  }
}
