import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { loadScrapedAdIds, updateScrapingStatus } from '@/lib/scrapers/session-utils';
// Typy są importowane z globalnego pliku src/types/global.d.ts

const prisma = new PrismaClient();

// POST /api/scrapings/resume - wznowienie przerwanego lub wstrzymanego scrapowania
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: 'ID sesji jest wymagane' },
        { status: 400 }
      );
    }
    
    const scrapingSession = await prisma.scrapingSession.findUnique({
      where: { id }
    });
    
    if (!scrapingSession) {
      return NextResponse.json(
        { error: 'Nie znaleziono sesji scrapowania' },
        { status: 404 }
      );
    }
    
    // Sprawdź, czy sesja jest w stanie pauzy i czy należy j<PERSON>e cze<PERSON>
    if (scrapingSession.status === 'paused') {
      const pausedAt = (scrapingSession as any).pausedAt;
      const pauseDuration = (scrapingSession as any).pauseDuration || 300; // domyślnie 5 minut
      
      if (pausedAt) {
        const pauseEndTime = new Date(pausedAt);
        pauseEndTime.setSeconds(pauseEndTime.getSeconds() + pauseDuration);
        
        if (new Date() < pauseEndTime) {
          // Jeszcze nie minął czas pauzy
          const remainingSeconds = Math.round((pauseEndTime.getTime() - new Date().getTime()) / 1000);
          return NextResponse.json(
            { 
              status: 'paused',
              message: `Scrapowanie jest wstrzymane. Pozostało ${remainingSeconds} sekund pauzy.`,
              resumeAt: pauseEndTime
            },
            { status: 200 }
          );
        }
      }
    }
    
    // Sprawdź, czy nie ma już innej aktywnej sesji (różnej od tej, którą próbujemy wznowić)
    const { hasActiveScrapingSession } = await import('@/lib/scrapers/session-utils');
    const isActiveSession = await hasActiveScrapingSession();
    
    if (isActiveSession) {
      // Pobierz aktywną sesję
      const activeSession = await prisma.scrapingSession.findFirst({
        where: {
          OR: [
            { status: 'in_progress' },
            { status: 'collecting_ids' },
            { status: 'collecting_details' }
          ]
        }
      });
      
      // Jeśli aktywna sesja to inna sesja niż ta, którą próbujemy wznowić
      if (activeSession && activeSession.id !== scrapingSession.id) {
        return NextResponse.json(
          { 
            error: 'Nie można wznowić tej sesji - inna sesja jest już aktywna',
            activeSessionId: activeSession.id
          },
          { status: 409 }
        );
      }
    }
    
    // Sprawdź, czy mamy dostęp do globalnych funkcji cron
    if (!global.cronFunctions || typeof global.cronFunctions.resumeScrapingSession !== 'function') {
      console.error('Brak dostępu do funkcji resumeScrapingSession');
      return NextResponse.json(
        { 
          error: 'Funkcja wznowienia scrapowania nie jest dostępna',
          success: false 
        },
        { status: 500 }
      );
    }
    
    // Aktualizuj status sesji na podstawie postępu
    let newStatus = 'in_progress';
    
    if (scrapingSession.adsIdsScraped === 0) {
      newStatus = 'collecting_ids'; // Zaczynamy od zbierania ID
    } else if (scrapingSession.adsIdsScraped > 0 && scrapingSession.adsDetailsScraped < scrapingSession.adsIdsScraped) {
      newStatus = 'collecting_details'; // Zaczynamy/kontynuujemy zbieranie szczegółów
    }
    
    // Załaduj zapisane wcześniej ID ogłoszeń
    let scrapedAdIds: string[] = [];
    if (scrapingSession.scrapedAdIds) {
      try {
        scrapedAdIds = JSON.parse(scrapingSession.scrapedAdIds);
        console.log(`Wczytano ${scrapedAdIds.length} ID ogłoszeń do wznowienia z sesji ${id}`);
      } catch (e) {
        console.error('Błąd podczas parsowania zapisanych ID ogłoszeń:', e);
      }
    }
    
    // Aktualizuj sesję w bazie danych
    const updateData: any = {
      status: newStatus,
      processedBatch: 0 // Resetujemy licznik partii
    };
    
    await prisma.scrapingSession.update({
      where: { id },
      data: updateData
    });
    
    // Ustaw zapisane ID w globalnym stanie przed wznowieniem
    // @ts-ignore - Ignorujemy błąd TypeScript, ponieważ sprawdzamy dostępność funkcji w czasie wykonania
    if (global.cronFunctions && typeof global.cronFunctions.setScrapedAdIds === 'function' && scrapedAdIds.length > 0) {
      try {
        // @ts-ignore - Ignorujemy błąd TypeScript, ponieważ sprawdzamy dostępność funkcji w czasie wykonania
        global.cronFunctions.setScrapedAdIds(scrapedAdIds);
        console.log(`Ustawiono ${scrapedAdIds.length} ID ogłoszeń w pamięci przed wznowieniem`);
      } catch (error) {
        console.error('Błąd podczas ustawiania ID ogłoszeń:', error);
      }
    }
    
    // Wywołaj globalną funkcję wznowienia scrapowania
    console.log(`Wywołanie funkcji wznowienia scrapowania dla sesji ID=${id}`);
    
    // Aktualizuj stan scrapowania przed wznowieniem
    updateScrapingStatus(true, scrapingSession.currentPage || 1);
    
    // Przekazujemy Id sesji i uruchamiamy scrapowanie w tle
    if (global.cronFunctions && typeof global.cronFunctions.resumeScrapingSession === 'function') {
      try {
        // Ponieważ funkcja działa asynchronicznie, nie czekamy na jej zakończenie
        global.cronFunctions.resumeScrapingSession(id)
          .then((result: any) => {
            console.log('Wynik wznowienia scrapowania:', result);
          })
          .catch((error: Error) => {
            console.error('Błąd podczas wznowienia scrapowania:', error);
            // W przypadku błędu, zresetuj stan scrapowania
            updateScrapingStatus(false);
          });
      } catch (error) {
        console.error('Błąd podczas wywoływania funkcji wznowienia:', error);
        // Zresetuj stan scrapowania
        updateScrapingStatus(false);
        
        return NextResponse.json(
          { error: 'Wystąpił błąd podczas wywoływania funkcji wznowienia' },
          { status: 500 }
        );
      }
    } else {
      console.error('Funkcja wznowienia scrapowania nie jest dostępna');
      return NextResponse.json(
        { error: 'Funkcja wznowienia scrapowania nie jest dostępna' },
        { status: 500 }
      );
    }
    
    // Przygotuj odpowiedź
    const response = {
      success: true,
      message: 'Rozpoczęto wznowienie sesji scrapowania',
      session: {
        ...scrapingSession,
        status: newStatus
      }
    };
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Błąd podczas próby wznowienia scrapowania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas próby wznowienia scrapowania' },
      { status: 500 }
    );
  }
}
