import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/scrapings - pobieranie listy sesji scrapowania
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '25');
    
    // Pobieranie łącznej liczby sesji
    const total = await prisma.scrapingSession.count();
    
    // Obliczanie offsetu na podstawie strony i limitu
    const skip = (page - 1) * limit;
    
    // Pobieranie sesji dla bieżącej strony
    const scrapingSessions = await prisma.scrapingSession.findMany({
      orderBy: {
        startedAt: 'desc'
      },
      skip,
      take: limit
    });
    
    // Obliczanie całkowitej liczby stron
    const totalPages = Math.ceil(total / limit);
    
    // Zwracanie danych z informacjami o paginacji
    return NextResponse.json({
      sessions: scrapingSessions,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Błąd podczas pobierania sesji scrapowania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania sesji scrapowania' },
      { status: 500 }
    );
  }
}

// POST /api/scrapings - utworzenie nowej sesji scrapowania
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { scrapingUrl, totalPages } = body;

    if (!scrapingUrl) {
      return NextResponse.json(
        { error: 'URL scrapowania jest wymagany' },
        { status: 400 }
      );
    }

    const scrapingSession = await prisma.scrapingSession.create({
      data: {
        scrapingUrl,
        totalPages: totalPages || 0,
        status: 'in_progress'
      }
    });

    return NextResponse.json(scrapingSession);
  } catch (error) {
    console.error('Błąd podczas tworzenia sesji scrapowania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas tworzenia sesji scrapowania' },
      { status: 500 }
    );
  }
}
