import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST() {
  try {
    // Sprawdź, czy ogłoszenie już istnieje
    const existingAd = await prisma.ad.findUnique({
      where: { externalId: "538405155" },
    });

    if (existingAd) {
      return NextResponse.json(
        { message: "Przykładowe ogłoszenie już istnieje", ad: existingAd },
        { status: 200 }
      );
    }

    // Dodaj przykładowe ogłoszenie
    const ad = await prisma.ad.create({
      data: {
        externalId: "538405155",
        brandModel: "Volkswagen Golf VII 1.6 TDI BMT Comfortline",
        desc: "Sprzedam zadbany samochód Volkswagen Golf VII z ekonomicznym silnikiem 1.6 TDI. <PERSON><PERSON><PERSON> w<PERSON>ciel, garażowany, bezwypadkowy.",
        date: "2023-05-15",
        location: "Warszawa, Mazowieckie",
        mileage: "89 000 km",
        year: "2018",
        price: "49 900 zł",
        url: "https://www.otomoto.pl/oferta/volkswagen-golf-vii-1-6-tdi-bmt-comfortline-ID538405155.html",
        smsSent: false,
        phones: {
          create: [
            { number: "+48 123 456 789" }
          ]
        }
      },
    });

    return NextResponse.json(
      { message: "Przykładowe ogłoszenie dodane pomyślnie", ad },
      { status: 201 }
    );
  } catch (error) {
    console.error("Błąd podczas dodawania przykładowego ogłoszenia:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas dodawania przykładowego ogłoszenia" },
      { status: 500 }
    );
  }
}
