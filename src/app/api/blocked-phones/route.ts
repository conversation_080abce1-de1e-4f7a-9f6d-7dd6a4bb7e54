import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";
    
    // Oblicz offset dla paginacji
    const skip = (page - 1) * limit;
    
    // Przygotuj warunki wyszukiwania
    const where: any = {};
    
    // Dodaj warunek wyszukiwania, jeśli podano
    if (search) {
      where.OR = [
        { number: { contains: search } },
        { reason: { contains: search } },
      ];
    }
    
    // Pobierz zablokowane numery z paginacją
    const phones = await prisma.blockedPhone.findMany({
      where,
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });
    
    // Pobierz całkowitą liczbę zablokowanych numerów spełniających kryteria
    const total = await prisma.blockedPhone.count({ where });
    
    return NextResponse.json({
      phones,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Błąd podczas pobierania zablokowanych numerów:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania zablokowanych numerów" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { number, reason } = await request.json();
    
    if (!number) {
      return NextResponse.json(
        { error: "Numer telefonu jest wymagany" },
        { status: 400 }
      );
    }
    
    // Walidacja numeru telefonu
    const phoneRegex = /^[0-9]{9}$/;
    if (!phoneRegex.test(number)) {
      return NextResponse.json(
        { error: "Nieprawidłowy format numeru telefonu. Wprowadź 9 cyfr bez spacji i znaków specjalnych." },
        { status: 400 }
      );
    }
    
    // Sprawdź, czy numer jest już na liście zablokowanych
    const existingPhone = await prisma.blockedPhone.findFirst({
      where: { number }
    });
    
    if (existingPhone) {
      return NextResponse.json(
        { error: "Ten numer telefonu jest już na liście zablokowanych" },
        { status: 400 }
      );
    }
    
    // Dodaj numer do listy zablokowanych
    const newBlockedPhone = await prisma.blockedPhone.create({
      data: { 
        number,
        reason: reason || undefined
      }
    });
    
    // Usuń numer z listy do wysyłki, jeśli istnieje
    const phoneToSend = await prisma.phoneToSend.findFirst({
      where: { number }
    });
    
    if (phoneToSend) {
      await prisma.phoneToSend.delete({
        where: { id: phoneToSend.id }
      });
    }
    
    return NextResponse.json(newBlockedPhone);
  } catch (error) {
    console.error("Błąd podczas dodawania zablokowanego numeru:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas dodawania zablokowanego numeru" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const id = request.nextUrl.searchParams.get("id");
    
    if (!id) {
      return NextResponse.json(
        { error: "ID zablokowanego numeru jest wymagane" },
        { status: 400 }
      );
    }
    
    // Usuń numer z listy zablokowanych
    await prisma.blockedPhone.delete({
      where: { id: parseInt(id) }
    });
    
    return NextResponse.json({ message: "Numer telefonu został usunięty z listy zablokowanych" });
  } catch (error) {
    console.error("Błąd podczas usuwania zablokowanego numeru:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas usuwania zablokowanego numeru" },
      { status: 500 }
    );
  }
}
