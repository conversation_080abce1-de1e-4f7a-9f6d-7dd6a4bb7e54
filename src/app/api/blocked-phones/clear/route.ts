import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST() {
  try {
    // Usuń wszystkie zablokowane numery
    await prisma.blockedPhone.deleteMany({});
    
    return NextResponse.json({ message: "Lista zablokowanych numerów telefonów została wyczyszczona" });
  } catch (error) {
    console.error("Błąd podczas czyszczenia listy zablokowanych numerów telefonów:", error);
    return NextResponse.json(
      { error: "Wys<PERSON>ą<PERSON>ł błąd podczas czyszczenia listy zablokowanych numerów telefonów" },
      { status: 500 }
    );
  }
}
