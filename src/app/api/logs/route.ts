import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { promises as fsPromises } from "fs";
import os from "os";

const MAX_LOGS = 30; // Zwiększono z 15 do 30
const MAX_LOG_SIZE_MB = 1; // Maksymalny rozmiar pliku logów w MB
const LOG_FILE_PATH = path.join(process.cwd(), "logs", "scraper.log");

interface LogEntry {
  timestamp: string;
  message: string;
  level: string;
}

// Funkcja pomocnicza do sprawdzania i czyszczenia pliku logów, jeśli jest za duży
async function checkAndCleanLogFile() {
  try {
    const stats = await fsPromises.stat(LOG_FILE_PATH);
    const fileSizeInMB = stats.size / (1024 * 1024);
    
    if (fileSizeInMB > MAX_LOG_SIZE_MB) {
      console.log(`Plik logów przekroczył ${MAX_LOG_SIZE_MB}MB (${fileSizeInMB.toFixed(2)}MB). Czyszczenie...`);
      
      // Opcja 1: Całkowite wyczyszczenie pliku
      // await fsPromises.writeFile(LOG_FILE_PATH, "");
      
      // Opcja 2: Zachowanie tylko ostatnich X linii
      const content = await fsPromises.readFile(LOG_FILE_PATH, "utf-8");
      const lines = content.split(/\r?\n/).filter(line => line.trim() !== "");
      const lastLines = lines.slice(-1000); // Zachowaj ostatnie 1000 linii
      await fsPromises.writeFile(LOG_FILE_PATH, lastLines.join(os.EOL) + os.EOL);
      
      console.log("Plik logów został wyczyszczony");
    }
  } catch (error) {
    console.error("Błąd podczas sprawdzania/czyszczenia pliku logów:", error);
  }
}

// Funkcja pomocnicza do inicjalizacji katalogu logów jeśli nie istnieje
async function ensureLogDirectory() {
  const logDir = path.join(process.cwd(), "logs");
  try {
    await fsPromises.access(logDir);
  } catch (error) {
    // Katalog nie istnieje, utwórz go
    await fsPromises.mkdir(logDir, { recursive: true });
  }
  
  // Sprawdź czy plik log istnieje, jeśli nie - utwórz pusty
  try {
    await fsPromises.access(LOG_FILE_PATH);
  } catch (error) {
    await fsPromises.writeFile(LOG_FILE_PATH, "");
  }
}

// Endpoint do pobierania logów w formacie JSON
export async function GET(request: NextRequest) {
  try {
    await ensureLogDirectory();
    
    // Sprawdź czy plik istnieje
    let logs: LogEntry[] = [];
    
    try {
      const fileContent = await fsPromises.readFile(LOG_FILE_PATH, "utf-8");
      const lines = fileContent.split(/\r?\n/).filter(line => line.trim() !== "");
      
      // Parsuj linie do formatu log
      logs = lines.map(line => {
        try {
          // Próba parsowania jako JSON (jeśli logi są zapisywane w formacie JSON)
          return JSON.parse(line);
        } catch (e) {
          // Fallback dla zwykłego formatu tekstowego: data poziom wiadomość
          const match = line.match(/^(\[.*?\])\s+(\w+)\s+(.*)$/);
          if (match) {
            return {
              timestamp: match[1].replace(/[\[\]]/g, ""),
              level: match[2].toLowerCase(),
              message: match[3]
            };
          }
          
          // Jeśli nie udało się sparsować w żadnym formacie, zwróć domyślny format
          return {
            timestamp: new Date().toISOString(),
            level: "info",
            message: line
          };
        }
      });
      
      // Sprawdź i wyczyść plik logów, jeśli jest za duży
      await checkAndCleanLogFile();
      
      // Weź tylko MAX_LOGS najnowszych wpisów i odwróć kolejność (najnowsze na górze)
      logs = logs.slice(-MAX_LOGS).reverse();
    } catch (error) {
      console.error("Błąd podczas odczytu pliku logów:", error);
    }
    
    // Sprawdź, czy żądanie dotyczy pobrania pliku
    const searchParams = request.nextUrl.searchParams;
    const download = searchParams.get("download");
    
    if (download === "true") {
      // Zwróć plik logów do pobrania
      try {
        const fileContent = await fsPromises.readFile(LOG_FILE_PATH, "utf-8");
        return new NextResponse(fileContent, {
          headers: {
            "Content-Type": "text/plain; charset=utf-8",
            "Content-Disposition": "attachment; filename=scraper.log"
          }
        });
      } catch (error) {
        console.error("Błąd podczas odczytu pliku logów do pobrania:", error);
        return NextResponse.json(
          { error: "Wystąpił błąd podczas pobierania pliku logów" },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json({ logs });
  } catch (error) {
    console.error("Błąd podczas pobierania logów:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania logów" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureLogDirectory();
    
    const { level, message } = await request.json();
    
    // Walidacja danych
    if (!message) {
      return NextResponse.json(
        { error: "Wiadomość jest wymagana" },
        { status: 400 }
      );
    }
    
    // Tworzenie wpisu log
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: level || "info",
      message
    };
    
    // Zapisz log do pliku
    await fsPromises.appendFile(
      LOG_FILE_PATH,
      JSON.stringify(logEntry) + os.EOL,
      "utf-8"
    );
    
    // Sprawdź i wyczyść plik logów, jeśli jest za duży
    await checkAndCleanLogFile();
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Błąd podczas zapisywania logu:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas zapisywania logu" },
      { status: 500 }
    );
  }
}
