import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcrypt";
import { generateToken } from "@/lib/auth/jwt";
import { cookies } from "next/headers";

// @ts-ignore - Ignoruj błędy TypeScript związane z Prisma
const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    // Sprawdź, czy email i hasło zostały podane
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email i hasło są wymagane" },
        { status: 400 }
      );
    }
    
    // Znajdź użytkownika po emailu
    // @ts-ignore - Ignoruj błędy TypeScript związane z Prisma
    const user = await prisma.User.findUnique({
      where: { email },
    });
    
    // Jeśli użytkownik nie istnieje lub hasło jest nieprawidłowe, <PERSON><PERSON><PERSON><PERSON><PERSON> błąd
    if (!user || !(await bcrypt.compare(password, user.password))) {
      return NextResponse.json(
        { error: "Nieprawidłowy email lub hasło" },
        { status: 401 }
      );
    }
    
    // Wygeneruj token JWT
    const token = await generateToken({
      userId: user.id,
      email: user.email,
    });
    
    // Ustaw ciasteczko z tokenem
    const response = NextResponse.json({
      token,
      user: {
        id: user.id,
        email: user.email,
      },
    });
    
    // Ustaw ciasteczko z tokenem - dostępne dla middleware
    response.cookies.set({
      name: "token",
      value: token,
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24, // 24 godziny
      sameSite: "strict",
    });
    
    // Ustaw drugie ciasteczko bez flagi httpOnly - dostępne dla JavaScript
    response.cookies.set({
      name: "auth_token",
      value: token,
      httpOnly: false,
      path: "/",
      maxAge: 60 * 60 * 24, // 24 godziny
      sameSite: "strict",
    });
    
    return response;
  } catch (error) {
    console.error("Błąd podczas logowania:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas logowania" },
      { status: 500 }
    );
  }
}
