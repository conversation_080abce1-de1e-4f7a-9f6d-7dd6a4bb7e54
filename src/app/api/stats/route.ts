import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

export async function GET() {
  try {
    // Obliczanie daty 30 dni wstecz
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Pobieranie ilości zescrapowanych ogłoszeń z ostatnich 30 dni
    const adCount = await prisma.ad.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    // Pobieranie ilości wysłanych SMS-ów z ostatnich 30 dni z tabeli SentSmsHistory
    const smsCount = await prisma.sentSmsHistory.count({
      where: {
        sentAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    // Pobieranie ceny SMS-a z ustawień
    const settings = await prisma.settings.findFirst();
    const smsPrice = settings?.smsPrice || 0.20;

    // Obliczanie kosztu SMS-ów
    const smsCost = smsCount * smsPrice;

    // Pobieranie statystyk z różnych okresów
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay()); // Początek tygodnia (niedziela)
    weekStart.setHours(0, 0, 0, 0);
    
    const monthStart = new Date();
    monthStart.setDate(1); // Pierwszy dzień miesiąca
    monthStart.setHours(0, 0, 0, 0);
    
    // Zliczanie SMS-ów z różnych okresów
    const todaySmsCount = await prisma.sentSmsHistory.count({
      where: {
        sentAt: {
          gte: todayStart
        }
      }
    });
    
    const weekSmsCount = await prisma.sentSmsHistory.count({
      where: {
        sentAt: {
          gte: weekStart
        }
      }
    });
    
    const monthSmsCount = await prisma.sentSmsHistory.count({
      where: {
        sentAt: {
          gte: monthStart
        }
      }
    });

    return NextResponse.json({
      adsScraped: adCount,
      smsSent: smsCount,
      smsCost: smsCost.toFixed(2),
      smsToday: todaySmsCount,
      smsThisWeek: weekSmsCount,
      smsThisMonth: monthSmsCount
    });
  } catch (error) {
    console.error("Błąd podczas pobierania statystyk:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania statystyk" },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
