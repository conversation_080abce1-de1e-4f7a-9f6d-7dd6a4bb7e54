import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { PuppeteerScraper } from "@/lib/scrapers/puppeteer-scraper";

const prisma = new PrismaClient();

export async function GET() {
  try {
    // Pobierz ustawienia scrapera z bazy danych
    const scraperSettings = await prisma.scraperSettings.findFirst();
    
    if (!scraperSettings) {
      return NextResponse.json(
        { error: "Nie znaleziono ustawień scrapera" },
        { status: 404 }
      );
    }

    // Pobierz URL do scrapowania z ustawień
    const settings = await prisma.settings.findFirst();
    
    if (!settings || !settings.scrapingUrl) {
      return NextResponse.json(
        { error: "Nie znaleziono URL do scrapowania w ustawieniach" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        message: "Ustawienia scrapera pobrane pomyślnie",
        settings: {
          url: settings.scrapingUrl,
          scraperType: scraperSettings.scraperType,
          headless: scraperSettings.headless,
          slowMo: scraperSettings.slowMo,
          userAgentRotation: scraperSettings.userAgentRotation
          // cookieAcceptSelector jest obsługiwany w innym miejscu
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Błąd podczas pobierania ustawień scrapera:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania ustawień scrapera" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Sprawdź, czy nie ma już aktywnej sesji scrapowania
    const { ensureSingleActiveSession } = await import('@/lib/scrapers/session-utils');
    const canStartNewSession = await ensureSingleActiveSession();
    
    if (!canStartNewSession) {
      return NextResponse.json(
        { error: "Nie można rozpocząć nowego scrapowania - inna sesja jest już aktywna" },
        { status: 409 }
      );
    }
    
    // Pobierz ustawienia scrapera z bazy danych
    const scraperSettings = await prisma.scraperSettings.findFirst();
    
    if (!scraperSettings) {
      return NextResponse.json(
        { error: "Nie znaleziono ustawień scrapera" },
        { status: 404 }
      );
    }

    // Pobierz URL do scrapowania z ustawień
    const settings = await prisma.settings.findFirst();
    
    if (!settings || !settings.scrapingUrl) {
      return NextResponse.json(
        { error: "Nie znaleziono URL do scrapowania w ustawieniach" },
        { status: 404 }
      );
    }

    // Dodaj parametr page=1 do URL, jeśli go nie ma
    let scrapingUrl = settings.scrapingUrl;
    if (!scrapingUrl.includes('page=')) {
      scrapingUrl += scrapingUrl.includes('?') ? '&page=1' : '?page=1';
    }

    console.log(`Rozpoczynanie scrapowania z URL: ${scrapingUrl}`);
    console.log(`Ustawienia: headless=${scraperSettings.headless}, turboMode=${(scraperSettings as any).turboMode || false}`);
    
    // Utwórz nową sesję scrapowania przed uruchomieniem scrapera
    // To zapewnia, że zawsze mamy aktywną sesję nawet przy pierwszym uruchomieniu
    const scrapingSession = await prisma.scrapingSession.create({
      data: {
        scrapingUrl: scrapingUrl,
        totalPages: 1, // Inicjalne ustawienie na 1 zamiast 0, aby uniknąć "Strony 1/0"
        currentPage: 1,
        status: 'in_progress'
      }
    });
    
    console.log(`Utworzono nową sesję scrapowania ID: ${scrapingSession.id}`);
    
    // Inicjalizuj scraper z ustawieniami
    const scraper = new PuppeteerScraper({
      headless: scraperSettings.headless,
      slowMo: scraperSettings.slowMo,
      userAgentRotation: scraperSettings.userAgentRotation,
      proxyEnabled: scraperSettings.proxyEnabled,
      proxyUrl: scraperSettings.proxyUrl || undefined,
      turboMode: (scraperSettings as any).turboMode || false,
      // cookieAcceptSelector jest obsługiwany w innym miejscu
    });

    // Uruchom scrapowanie z przekazaniem ID sesji
    const result = await scraper.scrapeAndSave(scrapingUrl, scrapingSession.id);
    
    // Aktualizuj sesję na podstawie wyniku
    if (result.success) {
      await prisma.scrapingSession.update({
        where: { id: scrapingSession.id },
        data: { 
          status: 'completed',
          finishedAt: new Date(),
          adsWithPhones: result.adsCount
        }
      });
      console.log(`Zakończono sesję scrapowania ID: ${scrapingSession.id} z ${result.adsCount} ogłoszeniami`);
    } else {
      await prisma.scrapingSession.update({
        where: { id: scrapingSession.id },
        data: { 
          status: 'interrupted',
          finishedAt: new Date()
        }
      });
      console.log(`Przerwano sesję scrapowania ID: ${scrapingSession.id} z powodu błędu`);
    }

    return NextResponse.json(
      { 
        message: result.message,
        success: result.success,
        adsCount: result.adsCount
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Błąd podczas scrapowania:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas scrapowania" },
      { status: 500 }
    );
  }
}
