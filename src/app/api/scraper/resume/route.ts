import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { PuppeteerScraper } from "@/lib/scrapers/puppeteer-scraper";
// Typy są importowane z globalnego pliku src/types/global.d.ts

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId } = body;
    
    if (!sessionId) {
      return NextResponse.json(
        { error: "ID sesji jest wymagane" },
        { status: 400 }
      );
    }
    
    // Pobierz sesję scrapowania
    const scrapingSession = await prisma.scrapingSession.findUnique({
      where: { id: sessionId }
    });
    
    if (!scrapingSession) {
      return NextResponse.json(
        { error: "Nie znaleziono sesji scrapowania" },
        { status: 404 }
      );
    }
    
    // Sprawdź, czy nie ma już innej aktywnej sesji (róż<PERSON>j od tej, kt<PERSON><PERSON><PERSON> próbu<PERSON><PERSON> w<PERSON>)
    const { hasActiveScrapingSession } = await import('@/lib/scrapers/session-utils');
    const isActiveSession = await hasActiveScrapingSession();
    
    if (isActiveSession) {
      // Pobierz aktywną sesję
      const activeSession = await prisma.scrapingSession.findFirst({
        where: {
          OR: [
            { status: 'in_progress' },
            { status: 'collecting_ids' },
            { status: 'collecting_details' }
          ]
        }
      });
      
      // Jeśli aktywna sesja to inna sesja niż ta, którą próbujemy wznowić
      if (activeSession && activeSession.id !== sessionId) {
        return NextResponse.json(
          { 
            error: "Nie można wznowić tej sesji - inna sesja jest już aktywna",
            activeSessionId: activeSession.id
          },
          { status: 409 }
        );
      }
    }
    
    // Pobierz ustawienia scrapera
    const scraperSettings = await prisma.scraperSettings.findFirst();
    
    if (!scraperSettings) {
      return NextResponse.json(
        { error: "Nie znaleziono ustawień scrapera" },
        { status: 404 }
      );
    }
    
    // Sprawdź czy mamy zapisane ID ogłoszeń w sesji
    let savedAdIds: string[] = [];
    if (scrapingSession.scrapedAdIds) {
      try {
        savedAdIds = JSON.parse(scrapingSession.scrapedAdIds);
        console.log(`Znaleziono ${savedAdIds.length} zapisanych ID ogłoszeń w sesji`);
        
        // Ustaw zapisane ID w globalnym stanie przed wznowieniem
        if (global.cronFunctions && typeof global.cronFunctions.setScrapedAdIds === 'function' && savedAdIds.length > 0) {
          try {
            // @ts-ignore - Ignorujemy błąd TypeScript, bo sprawdzamy dostępność funkcji w czasie wykonania
            global.cronFunctions.setScrapedAdIds(savedAdIds);
            console.log(`Ustawiono ${savedAdIds.length} ID ogłoszeń w pamięci przed wznowieniem`);
          } catch (error) {
            console.error('Błąd podczas ustawiania ID ogłoszeń:', error);
          }
        }
      } catch (e) {
        console.error('Błąd podczas parsowania zapisanych ID ogłoszeń:', e);
      }
    }
    
    // Pobierz URL do scrapowania
    const settings = await prisma.settings.findFirst();
    
    if (!settings || !settings.scrapingUrl) {
      return NextResponse.json(
        { error: "Nie znaleziono URL do scrapowania w ustawieniach" },
        { status: 404 }
      );
    }
    
    // Przygotuj URL z właściwym parametrem strony
    let scrapingUrl = settings.scrapingUrl;
    // Dodaj parametr page=X do URL, jeśli go nie ma
    if (scrapingUrl.includes('page=')) {
      // Zastąp istniejący parametr page nowym
      scrapingUrl = scrapingUrl.replace(/page=\d+/, `page=${scrapingSession.currentPage || 1}`);
    } else {
      // Dodaj parametr page
      scrapingUrl += scrapingUrl.includes('?') ? 
        `&page=${scrapingSession.currentPage || 1}` : 
        `?page=${scrapingSession.currentPage || 1}`;
    }
    
    // Inicjalizuj scraper z ustawieniami
    const scraper = new PuppeteerScraper({
      headless: scraperSettings.headless,
      slowMo: scraperSettings.slowMo,
      userAgentRotation: scraperSettings.userAgentRotation,
      proxyEnabled: scraperSettings.proxyEnabled,
      proxyUrl: scraperSettings.proxyUrl || undefined,
      turboMode: (scraperSettings as any).turboMode || false,
    });
    
    // Zaktualizuj status sesji
    await prisma.scrapingSession.update({
      where: { id: sessionId },
      data: { 
        status: 'in_progress',
        pausedAt: null,
        pauseReason: null
      }
    });
    
    // Uruchom scraper na bieżącej sesji
    console.log(`Wznawianie scrapowania z sesji ID=${sessionId}, URL=${scrapingUrl}, Status=${scrapingSession.status}`);
    console.log(`Sesja ma ${scrapingSession.adsIdsScraped || 0} zebranych ID ogłoszeń i ${scrapingSession.adsDetailsScraped || 0} przetworzonych ogłoszeń`);
    
    // Ten fragment jest zbędny, ponieważ już wcześniej odczytaliśmy ID ogłoszeń
    
    // Uruchom scrapowanie z odpowiednim URL
    const result = await scraper.scrapeAndSave(scrapingUrl);
    
    return NextResponse.json({
      success: result.success,
      message: result.message,
      adsCount: result.adsCount,
      session: scrapingSession
    });
  } catch (error: any) {
    console.error("Błąd podczas wznawiania scrapowania:", error);
    return NextResponse.json(
      { 
        error: "Wystąpił błąd podczas wznawiania scrapowania", 
        details: error.message || String(error) 
      },
      { status: 500 }
    );
  }
}
