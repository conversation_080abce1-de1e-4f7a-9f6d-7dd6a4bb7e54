import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET() {
  try {
    // Pobierz ustawienia z bazy danych
    const settings = await prisma.settings.findFirst();
    
    if (!settings) {
      // Domyślny URL z parametrem page=1
      let defaultUrl = "https://www.otomoto.pl/osobowe/od-2015/warszawa?search%5Bdist%5D=150&search%5Bfilter_enum_damaged%5D=0&search%5Bfilter_enum_registered%5D=1&search%5Bfilter_float_mileage%3Ato%5D=175000&search%5Bfilter_float_price%3Afrom%5D=50000&search%5Bfilter_float_year%3Ato%5D=2023&search%5Bprivate_business%5D=private&search%5Badvanced_search_expanded%5D=true";
      
      // Dodaj page=1 jeśli go nie ma
      if (!defaultUrl.includes('page=')) {
        defaultUrl += '&page=1';
      }
      
      // Jeśli nie ma ustawień, utwórz domyślne
      const defaultSettings = await prisma.settings.create({
        data: {
          smsContent: "Dzień dobry, jestem zainteresowany Twoim ogłoszeniem na OtoMoto. Czy auto jest jeszcze dostępne?",
          scrapingUrl: defaultUrl,
          cronEnabled: false,
          scrapingInterval: 60,
          smsPrice: 0.20,
          smsResendDays: 90,
        }
      });
      
      return NextResponse.json(defaultSettings);
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error("Błąd podczas pobierania ustawień:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania ustawień" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Upewnij się, że URL zawiera parametr page=1
    let scrapingUrl = data.scrapingUrl;
    if (scrapingUrl && !scrapingUrl.includes('page=')) {
      scrapingUrl = scrapingUrl + (scrapingUrl.includes('?') ? '&page=1' : '?page=1');
      console.log("Dodano parametr page=1 do URL:", scrapingUrl);
    }
    
    // Pobierz istniejące ustawienia
    const existingSettings = await prisma.settings.findFirst();
    
    if (existingSettings) {
      // Aktualizuj istniejące ustawienia
      const updatedSettings = await prisma.settings.update({
        where: { id: existingSettings.id },
        data: {
          smsContent: data.smsContent !== undefined ? data.smsContent : existingSettings.smsContent,
          scrapingUrl: scrapingUrl !== undefined ? scrapingUrl : existingSettings.scrapingUrl,
          cronEnabled: data.cronEnabled !== undefined ? data.cronEnabled : existingSettings.cronEnabled,
          scrapingInterval: data.scrapingInterval !== undefined ? data.scrapingInterval : existingSettings.scrapingInterval,
          smsPrice: data.smsPrice !== undefined ? data.smsPrice : existingSettings.smsPrice,
          smsResendDays: data.smsResendDays !== undefined ? data.smsResendDays : existingSettings.smsResendDays,
        }
      });
      
      return NextResponse.json(updatedSettings);
    } else {
      // Obsługa domyślnego URL
      let defaultUrl = "https://www.otomoto.pl/osobowe/od-2015/warszawa?search%5Bdist%5D=150&search%5Bfilter_enum_damaged%5D=0&search%5Bfilter_enum_registered%5D=1&search%5Bfilter_float_mileage%3Ato%5D=175000&search%5Bfilter_float_price%3Afrom%5D=50000&search%5Bfilter_float_year%3Ato%5D=2023&search%5Bprivate_business%5D=private&search%5Badvanced_search_expanded%5D=true";
      
      // Użyj podanego URL lub domyślnego
      let finalUrl = scrapingUrl || defaultUrl;
      
      // Upewnij się, że URL zawiera parametr page=1
      if (!finalUrl.includes('page=')) {
        finalUrl += (finalUrl.includes('?') ? '&page=1' : '?page=1');
        console.log("Dodano parametr page=1 do URL przy tworzeniu nowych ustawień:", finalUrl);
      }
      
      // Utwórz nowe ustawienia
      const newSettings = await prisma.settings.create({
        data: {
          smsContent: data.smsContent || "Dzień dobry, jestem zainteresowany Twoim ogłoszeniem na OtoMoto. Czy auto jest jeszcze dostępne?",
          scrapingUrl: finalUrl,
          cronEnabled: data.cronEnabled || false,
          scrapingInterval: data.scrapingInterval || 60,
          smsPrice: data.smsPrice || 0.20,
          smsResendDays: data.smsResendDays || 90,
        }
      });
      
      return NextResponse.json(newSettings);
    }
  } catch (error) {
    console.error("Błąd podczas aktualizacji ustawień:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas aktualizacji ustawień" },
      { status: 500 }
    );
  }
}
