import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// GET - Pobierz wszystkie przypomnienia o leasingach
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Sprawdź, czy żądanie dotyczy tylko liczby przypomnień na dzisiaj
    if (searchParams.get("countToday") === "true") {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const count = await prisma.leaseReminder.count({
        where: {
          expirationDate: {
            gte: today,
            lt: tomorrow,
          },
        },
      });

      return NextResponse.json({ count });
    }
    
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "expirationDate";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    // Buduj warunki filtrowania
    const where = search
      ? {
          OR: [
            { fullName: { contains: search } },
            { phone: { contains: search } },
            { email: { contains: search } },
            { carDetails: { contains: search } },
            { note: { contains: search } },
            { status: { contains: search } },
          ],
        }
      : {};

    // Pobierz dane z paginacją
    const [reminders, total] = await Promise.all([
      prisma.leaseReminder.findMany({
        where,
        take: limit,
        skip: (page - 1) * limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      prisma.leaseReminder.count({ where }),
    ]);

    // Zwróć dane z informacjami o paginacji
    return NextResponse.json({
      reminders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Błąd podczas pobierania przypomnień:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas pobierania przypomnień" },
      { status: 500 }
    );
  }
}

// POST - Dodaj nowe przypomnienie o leasingu
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Konwersja daty z formatu string do Date
    if (data.expirationDate && typeof data.expirationDate === 'string') {
      data.expirationDate = new Date(data.expirationDate);
    }

    // Walidacja danych
    if (!data.fullName || !data.phone || !data.carDetails || !data.expirationDate) {
      return NextResponse.json(
        { error: "Brakuje wymaganych pól" },
        { status: 400 }
      );
    }

    // Dodaj nowe przypomnienie
    const reminder = await prisma.leaseReminder.create({
      data,
    });

    return NextResponse.json(reminder, { status: 201 });
  } catch (error) {
    console.error("Błąd podczas dodawania przypomnienia:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas dodawania przypomnienia" },
      { status: 500 }
    );
  }
}

// PUT - Aktualizuj przypomnienie o leasingu
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    const { id, ...updateData } = data;

    if (!id) {
      return NextResponse.json(
        { error: "ID przypomnienia jest wymagane" },
        { status: 400 }
      );
    }

    // Konwersja daty z formatu string do Date
    if (updateData.expirationDate && typeof updateData.expirationDate === 'string') {
      updateData.expirationDate = new Date(updateData.expirationDate);
    }

    // Aktualizuj przypomnienie
    const reminder = await prisma.leaseReminder.update({
      where: { id: parseInt(id.toString(), 10) },
      data: updateData,
    });

    return NextResponse.json(reminder);
  } catch (error) {
    console.error("Błąd podczas aktualizacji przypomnienia:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas aktualizacji przypomnienia" },
      { status: 500 }
    );
  }
}

// DELETE - Usuń przypomnienie o leasingu
export async function DELETE(request: NextRequest) {
  try {
    const data = await request.json();
    const { id } = data;

    if (!id) {
      return NextResponse.json(
        { error: "ID przypomnienia jest wymagane" },
        { status: 400 }
      );
    }

    // Usuń przypomnienie
    await prisma.leaseReminder.delete({
      where: { id: parseInt(id.toString(), 10) },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Błąd podczas usuwania przypomnienia:", error);
    return NextResponse.json(
      { error: "Wystąpił błąd podczas usuwania przypomnienia" },
      { status: 500 }
    );
  }
}
