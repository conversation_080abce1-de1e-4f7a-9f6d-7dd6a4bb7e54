"use client";

import { useState, useEffect, useRef } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { getFromApi, postToApi, deleteFromApi } from "@/lib/utils/api";
import { UploadIcon, Cross2Icon } from "@radix-ui/react-icons";

interface PhoneToSend {
  id: number;
  number: string;
  createdAt: string;
}

interface ImportSummary {
  imported: number;
  phonesFound: number;
  adsUpdated: number;
  smsHistoryAdded: number;
  phonesToSendDeleted: number;
}

export default function PhoneListPage() {
  const [phones, setPhones] = useState<PhoneToSend[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newPhone, setNewPhone] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSendSmsConfirm, setShowSendSmsConfirm] = useState(false);
  const [sendingSms, setSendingSms] = useState(false);
  const [batchSize, setBatchSize] = useState(100); // Domyślny rozmiar partii
  
  // Stan dla modala importu
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFormat, setImportFormat] = useState<"txt" | "json" | "csv">("txt");
  const [importFile, setImportFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportSummary | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25, // Zmieniono z 10 na 25 zgodnie z wymaganiami
    total: 0,
    totalPages: 0,
  });
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    fetchPhones();
  }, [pagination.page, search]);

  const fetchPhones = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (search) {
        queryParams.append("search", search);
      }
      
      const response = await getFromApi(`/api/phones-to-send?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać listy numerów");
      }
      
      const data = await response.json();
      
      if (data.phones && data.pagination) {
        setPhones(data.phones);
        setPagination(data.pagination);
      } else {
        // Zachowanie kompatybilności ze starym API, które może zwracać tylko tablicę
        setPhones(data);
      }
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania listy numerów");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  const handleAddPhone = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPhone) return;
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Walidacja numeru telefonu
      const phoneRegex = /^[0-9]{9}$/;
      if (!phoneRegex.test(newPhone)) {
        setError("Nieprawidłowy format numeru telefonu. Wprowadź 9 cyfr bez spacji i znaków specjalnych.");
        setLoading(false);
        return;
      }
      
      const response = await postToApi("/api/phones-to-send", { number: newPhone });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nie udało się dodać numeru");
      }
      
      const addedPhone = await response.json();
      // Odśwież listę numerów, aby uwzględnić nowy podział na strony
      await fetchPhones();
      setSuccess(`Numer ${addedPhone.number} został dodany pomyślnie`);
      setNewPhone("");
    } catch (error) {
      setError(error instanceof Error ? error.message : "Wystąpił błąd podczas dodawania numeru");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePhone = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await deleteFromApi(`/api/phones-to-send?id=${id}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć numeru");
      }
      
      // Odśwież listę numerów zamiast lokalnie filtrować
      await fetchPhones();
      setSuccess("Numer został usunięty pomyślnie");
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania numeru");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendAllSms = async () => {
    try {
      setSendingSms(true);
      setError(null);
      setSuccess(null);
      
      const response = await postToApi("/api/sms/send", { 
        sendAll: true,
        batchSize: batchSize 
      });
      
      const result = await response.json();
      
      // Obsługa odpowiedzi z API
      if (!response.ok || !result.success) {
        // Wyświetl komunikat o błędzie z API
        setError(result.errorMessage || result.message || "Nie udało się wysłać SMS-ów");
        console.error("Błąd wysyłki SMS:", result);
      } else {
        // Wyświetl komunikat o sukcesie
        setSuccess(`SMS-y zostały wysłane pomyślnie (${result.sentCount || 'wszystkie'})`);
      }
      
      setShowSendSmsConfirm(false);
      
      // Odśwież listę numerów po wysłaniu SMS-ów (niezależnie od wyniku)
      await fetchPhones();
    } catch (error) {
      setError(error instanceof Error ? error.message : "Wystąpił błąd podczas wysyłania SMS-ów");
      console.error(error);
    } finally {
      setSendingSms(false);
    }
  };

  const handleDeleteAll = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await postToApi("/api/phones-to-send/clear", {});
      
      if (!response.ok) {
        throw new Error("Nie udało się wyczyścić listy numerów");
      }
      
      setPhones([]);
      setPagination({ ...pagination, total: 0, totalPages: 0 });
      setSuccess("Lista numerów została wyczyszczona pomyślnie");
      setShowDeleteConfirm(false);
    } catch (error) {
      setError("Wystąpił błąd podczas czyszczenia listy numerów");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  
  // Obsługa wyboru pliku
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0]);
    }
  };
  
  // Obsługa importu pliku
  const handleImport = async () => {
    if (!importFile) {
      setError("Nie wybrano pliku");
      return;
    }
    
    try {
      setIsImporting(true);
      setError(null);
      setSuccess(null);
      setImportResult(null);
      
      const formData = new FormData();
      formData.append('file', importFile);
      formData.append('format', importFormat);
      
      const response = await fetch('/api/phones-to-send/import', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || "Nie udało się zaimportować numerów");
      }
      
      setSuccess(`Zaimportowano ${result.summary.imported} numerów telefonów`);
      setImportResult(result.summary);
      
      // Odśwież listę numerów po imporcie
      await fetchPhones();
      
      // Zresetuj stan
      setImportFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "Wystąpił błąd podczas importu numerów");
      console.error(error);
    } finally {
      setIsImporting(false);
    }
  };
  
  // Zamykanie modala importu
  const closeImportModal = () => {
    setShowImportModal(false);
    setImportFile(null);
    setImportResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Layout>
      <h1 className="mb-6 text-2xl font-semibold text-gray-900 dark:text-white">Lista numerów do wysyłki</h1>
      
      {error && (
        <div className="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
          {error}
        </div>
      )}
      
      {success && (
        <div className="px-4 py-3 mb-4 text-green-700 bg-green-100 border border-green-400 rounded">
          {success}
        </div>
      )}
      
      <div className="overflow-hidden bg-white rounded-lg shadow dark:bg-gray-800">
        <div className="flex flex-wrap items-center justify-between px-4 py-5 border-b border-gray-200 sm:px-6 dark:border-gray-700">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Lista numerów</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              na które będą wysyłane SMS-y
            </p>
          </div>
          
          <div className="flex items-center gap-2 mt-2 sm:mt-0">
            <Button
              variant="outline"
              onClick={() => setShowImportModal(true)}
              className="flex items-center gap-1"
            >
              <UploadIcon className="w-4 h-4" />
              Importuj z pliku
            </Button>
            <div className="flex items-center gap-2">
              <select
                value={batchSize}
                onChange={(e) => setBatchSize(Number(e.target.value))}
                className="block border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="1">1</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="150">150</option>
                <option value="200">200</option>
                <option value="250">250</option>
              </select>
              <Button
                variant="default"
                onClick={() => setShowSendSmsConfirm(true)}
                disabled={loading || sendingSms || phones.length === 0}
              >
                Wyślij SMS-y
              </Button>
            </div>
            <Button
              variant="destructive"
              onClick={() => setShowDeleteConfirm(true)}
              disabled={loading || sendingSms || phones.length === 0}
            >
              Wyczyść listę
            </Button>
          </div>
        </div>
        
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <form onSubmit={handleAddPhone} className="flex flex-col gap-2 sm:flex-row">
            <input
              type="text"
              value={newPhone}
              onChange={(e) => setNewPhone(e.target.value)}
              placeholder="Numer telefonu (9 cyfr)"
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              maxLength={9}
            />
            <Button
              type="submit"
              disabled={loading || !newPhone}
            >
              Dodaj numer
            </Button>
          </form>
        </div>
        
        {/* Modal importu pliku */}
        {showImportModal && (
          <div className="p-6 bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Importuj numery telefonów z pliku</h3>
              <button
                onClick={closeImportModal}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                <Cross2Icon className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Format pliku
                </label>
                <div className="flex flex-wrap gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="txt"
                      checked={importFormat === "txt"}
                      onChange={() => setImportFormat("txt")}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">TXT (jeden numer w linii)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="csv"
                      checked={importFormat === "csv"}
                      onChange={() => setImportFormat("csv")}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">CSV</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="json"
                      checked={importFormat === "json"}
                      onChange={() => setImportFormat("json")}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">JSON</span>
                  </label>
                </div>
              </div>
              
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Wybierz plik
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={importFormat === "txt" ? ".txt" : importFormat === "csv" ? ".csv" : ".json"}
                  onChange={handleFileChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:text-gray-400 dark:file:bg-blue-900/20 dark:file:text-blue-300"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {importFormat === "txt" 
                    ? "Plik TXT powinien zawierać jeden numer telefonu w każdej linii." 
                    : importFormat === "csv"
                    ? "Plik CSV z numerami telefonów w kolumnie. Wartości tekstowe są ignorowane."
                    : "Plik JSON powinien zawierać tablicę numerów telefonów lub obiektów z numerami."}
                </p>
              </div>
              
              {importResult && (
                <div className="p-4 rounded-md bg-blue-50 dark:bg-blue-900/20">
                  <h4 className="mb-2 font-medium text-blue-700 dark:text-blue-300">Wynik importu:</h4>
                  <ul className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                    <li>Zaimportowano: {importResult.imported} numerów</li>
                    <li>Znaleziono w bazie: {importResult.phonesFound} numerów</li>
                    <li>Zaktualizowano ogłoszeń: {importResult.adsUpdated}</li>
                    <li>Dodano do historii SMS: {importResult.smsHistoryAdded}</li>
                    <li>Usunięto z listy do wysyłki: {importResult.phonesToSendDeleted}</li>
                  </ul>
                </div>
              )}
              
              <div className="flex justify-end mt-4">
                <Button
                  variant="default"
                  onClick={handleImport}
                  disabled={isImporting || !importFile}
                >
                  {isImporting ? 'Importowanie...' : 'Importuj'}
                </Button>
              </div>
            </div>
          </div>
        )}
        
        {showSendSmsConfirm && (
          <div className="p-4 border-b border-gray-200 bg-blue-50 dark:bg-blue-900/20 dark:border-gray-700">
            <p className="mb-4 text-blue-700 dark:text-blue-400">
              Czy na pewno chcesz wysłać SMS-y do {batchSize} najstarszych numerów na liście? 
              Ta operacja spowoduje naliczenie opłat za wysłane wiadomości.
            </p>
            <div className="flex space-x-2">
              <Button
                variant="default"
                onClick={handleSendAllSms}
                disabled={sendingSms}
              >
                {sendingSms ? 'Wysyłanie...' : 'Tak, wyślij SMS-y'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowSendSmsConfirm(false)}
                disabled={sendingSms}
              >
                Anuluj
              </Button>
            </div>
          </div>
        )}
        
        {showDeleteConfirm && (
          <div className="p-4 border-b border-gray-200 bg-red-50 dark:bg-red-900/20 dark:border-gray-700">
            <p className="mb-4 text-red-700 dark:text-red-400">
              Czy na pewno chcesz usunąć wszystkie numery? Tej operacji nie można cofnąć.
            </p>
            <div className="flex space-x-2">
              <Button
                variant="destructive"
                onClick={handleDeleteAll}
                disabled={loading}
              >
                Tak, usuń wszystkie
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={loading}
              >
                Anuluj
              </Button>
            </div>
          </div>
        )}
        
        {loading && !phones.length ? (
          <div className="p-6 text-center">
            <div className="w-12 h-12 mx-auto border-b-2 border-blue-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie numerów...</p>
          </div>
        ) : phones.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            Brak numerów na liście
          </div>
        ) : (
          <>
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {phones.map((phone) => (
                <li key={phone.id} className="flex items-center justify-between px-6 py-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{phone.number}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Dodano: {new Date(phone.createdAt).toLocaleString('pl-PL')}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeletePhone(phone.id)}
                    disabled={loading}
                    className="text-red-600 border-red-600 hover:bg-red-50 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-900/20"
                  >
                    Usuń
                  </Button>
                </li>
              ))}
            </ul>
            {/* Paginacja */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
                <div className="flex justify-between flex-1 sm:hidden">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    Poprzednia
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    Następna
                  </Button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                      <span className="font-medium">
                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                      </span>{' '}
                      z <span className="font-medium">{pagination.total}</span> numerów
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 rounded-l-md dark:border-gray-600"
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <span className="sr-only">Pierwsza</span>
                        «
                      </Button>
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <span className="sr-only">Poprzednia</span>
                        ‹
                      </Button>
                      
                      {/* Numery stron */}
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum;
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1;
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i;
                        } else {
                          pageNum = pagination.page - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNum}
                            variant={pagination.page === pageNum ? "default" : "outline"}
                            className="relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={loading}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                      
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        <span className="sr-only">Następna</span>
                        ›
                      </Button>
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 rounded-r-md dark:border-gray-600"
                        onClick={() => handlePageChange(pagination.totalPages)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        <span className="sr-only">Ostatnia</span>
                        »
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
}
