"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { getFromApi } from "@/lib/utils/api";

interface PhoneInQuarantine {
  id: number;
  number: string;
  sentAt: string;
  expiresAt: string;
  daysLeft: number;
}

export default function QuarantineListPage() {
  const [phones, setPhones] = useState<PhoneInQuarantine[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [quarantinePeriod, setQuarantinePeriod] = useState<number>(90); // <PERSON><PERSON><PERSON><PERSON>a warto<PERSON>
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
    totalPages: 0,
  });
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    fetchQuarantineSettings();
    fetchPhones();
  }, [pagination.page, search]);

  const fetchQuarantineSettings = async () => {
    try {
      const response = await getFromApi(`/api/settings`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać ustawień karencji");
      }
      
      const data = await response.json();
      
      setQuarantinePeriod(data.smsResendDays);
    } catch (error) {
      console.error("Błąd podczas pobierania ustawień karencji:", error);
    }
  };

  const fetchPhones = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (search) {
        queryParams.append("search", search);
      }
      
      const response = await getFromApi(`/api/phones/quarantine?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać listy numerów w karencji");
      }
      
      const data = await response.json();
      
      if (data.phones && data.pagination) {
        setPhones(data.phones);
        setPagination(data.pagination);
      } else {
        // Zachowanie kompatybilności ze starym API, które może zwracać tylko tablicę
        setPhones(data);
      }
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania listy numerów w karencji");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Lista numerów w karencji</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Lista numerów w karencji</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Numery, które otrzymały SMS i są obecnie w okresie karencji ({quarantinePeriod} dni)
            </p>
          </div>
          
          <div className="mt-2 sm:mt-0 flex gap-2">
            <form onSubmit={handleSearch} className="flex gap-2">
              <input
                type="search"
                placeholder="Szukaj numeru..."
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
              />
              <Button type="submit" disabled={loading || !searchInput.trim()}>
                Szukaj
              </Button>
            </form>
          </div>
        </div>
        
        {loading && !phones.length ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie numerów...</p>
          </div>
        ) : phones.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            Brak numerów w karencji
          </div>
        ) : (
          <>
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {phones.map((phone) => (
                <li key={phone.id} className="px-6 py-4 flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{phone.number}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      SMS wysłany / zaimportowany: {new Date(phone.sentAt).toLocaleString('pl-PL')}
                    </p>
                  </div>
                  <div className={`text-sm font-medium rounded-md px-3 py-1 ${
                    phone.daysLeft <= 7 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                  }`}>
                    {phone.daysLeft <= 0 
                      ? 'Karencja wygasła'
                      : `Karencja wygasa za ${phone.daysLeft} ${phone.daysLeft === 1 ? 'dzień' : 
                         phone.daysLeft < 5 ? 'dni' : 'dni'}`}
                  </div>
                </li>
              ))}
            </ul>
            {/* Paginacja */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    Poprzednia
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    Następna
                  </Button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                      <span className="font-medium">
                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                      </span>{' '}
                      z <span className="font-medium">{pagination.total}</span> numerów
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <span className="sr-only">Pierwsza</span>
                        «
                      </Button>
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <span className="sr-only">Poprzednia</span>
                        ‹
                      </Button>
                      
                      {/* Numery stron */}
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum;
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1;
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i;
                        } else {
                          pageNum = pagination.page - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNum}
                            variant={pagination.page === pageNum ? "default" : "outline"}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={loading}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                      
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        <span className="sr-only">Następna</span>
                        ›
                      </Button>
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pagination.totalPages)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        <span className="sr-only">Ostatnia</span>
                        »
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
}
