"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { PlayIcon, ClockIcon, InfoCircledIcon, CheckCircledIcon, CrossCircledIcon, ReloadIcon, PauseIcon, TrashIcon, StopIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast";
import { getFromApi } from "@/lib/utils/api";
import { useRouter } from "next/navigation";
import Link from "next/link";
import ScrapingProgress from "@/components/ScrapingProgress";

// Typy danych dla sesji scrapowania
interface ScrapingSession {
  id: number;
  startedAt: string;
  finishedAt: string | null;
  totalPages: number;
  currentPage: number;
  status: string;
  adsIdsScraped: number;
  adsDetailsScraped: number;
  adsWithPhones: number;
  totalAdsIds: number;
  scrapingUrl: string;
  createdAt: string;
  updatedAt: string;
  pausedAt?: string | null;
  pauseDuration?: number | null;
  pauseReason?: string | null;
  // Dodatkowe pola obliczone na serwerze
  pageProgress?: number;
  adsIdsProgress?: number;
  adsDetailsProgress?: number;
  adsWithPhonesProgress?: number;
}

interface CronStatus {
  enabled: boolean;
  interval: number;
  lastScrapedAt: string | null;
  isActive: boolean;
  currentScrapingPage: number | null;
  isScrapingActive: boolean;
  error?: string;
}

const formatDate = (dateString: string | null) => {
  if (!dateString) return "—";
  const date = new Date(dateString);
  return date.toLocaleString('pl');
};

const formatDuration = (startDate: string, endDate: string | null) => {
  if (!endDate) return "Trwa...";
  
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();
  const durationMs = end - start;
  
  const seconds = Math.floor(durationMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

// Formatowanie procentów z obsługą wartości undefined
const formatPercent = (value?: number) => {
  if (value === undefined) return "0%";
  return `${value}%`;
};

const StatusBadge = ({ status }: { status: string }) => {
  if (status === 'in_progress') {
    return (
      <div className="flex items-center">
        <ClockIcon className="text-amber-500 mr-1" />
        <span className="text-amber-500 font-medium">W trakcie</span>
      </div>
    );
  } else if (status === 'collecting_ids') {
    return (
      <div className="flex items-center">
        <ReloadIcon className="text-blue-500 mr-1 animate-spin" />
        <span className="text-blue-500 font-medium">Zbieram ID</span>
      </div>
    );
  } else if (status === 'collecting_details') {
    return (
      <div className="flex items-center">
        <ReloadIcon className="text-indigo-500 mr-1 animate-spin" />
        <span className="text-indigo-500 font-medium">Zbieram dane</span>
      </div>
    );
  } else if (status === 'paused') {
    return (
      <div className="flex items-center">
        <PauseIcon className="text-yellow-500 mr-1" />
        <span className="text-yellow-500 font-medium">Pauza</span>
      </div>
    );
  } else if (status === 'completed') {
    return (
      <div className="flex items-center">
        <CheckCircledIcon className="text-green-500 mr-1" />
        <span className="text-green-500 font-medium">Zakończono</span>
      </div>
    );
  } else if (status === 'interrupted') {
    return (
      <div className="flex items-center">
        <CrossCircledIcon className="text-red-500 mr-1" />
        <span className="text-red-500 font-medium">Przerwano</span>
      </div>
    );
  } else {
    return (
      <div className="flex items-center">
        <InfoCircledIcon className="text-blue-500 mr-1" />
        <span className="text-blue-500 font-medium">{status}</span>
      </div>
    );
  }
};

export default function ScrapingsPage() {
  const router = useRouter();
  const [currentSession, setCurrentSession] = useState<ScrapingSession | null>(null);
  const [sessions, setSessions] = useState<ScrapingSession[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isClearing, setIsClearing] = useState<boolean>(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0,
    totalPages: 0,
  });
  const [cronStatus, setCronStatus] = useState<CronStatus | null>(null);
  const [loadingCronStatus, setLoadingCronStatus] = useState(false);
  const [updatingCron, setUpdatingCron] = useState(false);
  const { showToast } = useToast();

  // Pobierz status crona
  const fetchCronStatus = async () => {
    try {
      setLoadingCronStatus(true);
      const response = await fetch("/api/cron");
      
      if (response.ok) {
        const data = await response.json();
        setCronStatus(data);
      } else {
        console.error("Nie udało się pobrać statusu crona");
      }
    } catch (error) {
      console.error("Błąd podczas pobierania statusu crona:", error);
    } finally {
      setLoadingCronStatus(false);
    }
  };

  // Aktualizuj status crona
  const updateCronStatus = async (enabled?: boolean, interval?: number, runNow?: boolean, cleanupSmsHistory?: boolean, stopScraping?: boolean) => {
    try {
      setUpdatingCron(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch("/api/cron", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          enabled,
          interval,
          runNow,
          cleanupSmsHistory,
          stopScraping
        }),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zaktualizować statusu crona");
      }
      
      const data = await response.json();
      setCronStatus(data.status);
      
      let successMessage = "Status crona zaktualizowany pomyślnie";
      if (runNow) {
        successMessage = "Scrapowanie uruchomione ręcznie";
        // Odśwież stronę po uruchomieniu scrapowania
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else if (stopScraping) {
        successMessage = "Scrapowanie zatrzymane";
        setSuccess(successMessage);
        
        // Dodatkowe czyszczenie aktywnych sesji po zatrzymaniu scrapowania
        try {
          const clearUrl = new URL('/api/scrapings/clear', window.location.origin);
          clearUrl.searchParams.set('activeOnly', 'true');
          
          await fetch(clearUrl.toString(), {
            method: 'DELETE',
          });
          
          // Odśwież całą stronę po zatrzymaniu i usunięciu aktywnych sesji
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } catch (clearError) {
          console.error('Błąd podczas czyszczenia aktywnych sesji:', clearError);
        }
        
      } else if (cleanupSmsHistory) {
        const deletedCount = data.status.deletedSmsHistoryCount || 0;
        successMessage = `Usunięto ${deletedCount} starych wpisów z historii SMS-ów`;
        showToast({
          message: successMessage,
          type: "success",
          duration: 5000
        });
      } else {
        setSuccess(successMessage);
      }
      
      // Odśwież dane o aktualnej sesji
      fetchCurrentSession();
    } catch (error) {
      setError("Wystąpił błąd podczas aktualizacji statusu crona");
      console.error(error);
    } finally {
      setUpdatingCron(false);
    }
  };

  const fetchCurrentSession = async () => {
    try {
      const response = await getFromApi('/api/scrapings/current');
      
      if (response.ok) {
        const data = await response.json();
        if (data && data.id) {
          setCurrentSession(data);
        }
      }
    } catch (error) {
      console.error('Błąd podczas pobierania aktualnej sesji scrapowania:', error);
    }
  };

  const fetchSessions = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });
      
      const response = await getFromApi(`/api/scrapings?${queryParams.toString()}`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.sessions && data.pagination) {
          // Filtruj sesje z totalPages === 1
          const filteredSessions = data.sessions.filter((session: ScrapingSession) => session.totalPages !== 1);
          setSessions(filteredSessions);
          setPagination({
            ...data.pagination,
            total: data.pagination.total - (data.sessions.length - filteredSessions.length)
          });
        } else if (Array.isArray(data)) {
          // Zachowanie kompatybilności ze starym API, które może zwracać tylko tablicę
          const filteredSessions = data.filter((session: ScrapingSession) => session.totalPages !== 1);
          setSessions(filteredSessions);
        }
      }
    } catch (error) {
      console.error('Błąd podczas pobierania sesji scrapowania:', error);
      setError('Wystąpił błąd podczas pobierania danych sesji scrapowania');
    } finally {
      setLoading(false);
    }
  };
  
  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  const handleResume = async (id: number) => {
    try {
      const response = await fetch('/api/scrapings/resume', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setSuccess('Wznowiono scrapowanie');
        showToast({
          message: 'Wznowiono scrapowanie',
          type: 'success',
          duration: 3000
        });
        fetchSessions();
        fetchCurrentSession();
      } else {
        setError(data.error || 'Funkcja wznowienia jest obecnie niedostępna');
        showToast({
          message: data.error || 'Funkcja wznowienia jest obecnie niedostępna',
          type: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      setError('Wystąpił błąd podczas próby wznowienia scrapowania');
      showToast({
        message: 'Wystąpił błąd podczas próby wznowienia scrapowania',
        type: 'error',
        duration: 5000
      });
    }
  };
  
  const handlePause = async (id: number) => {
    try {
      const response = await fetch('/api/scrapings/pause', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setSuccess('Zatrzymano scrapowanie');
        showToast({
          message: 'Zatrzymano scrapowanie',
          type: 'success',
          duration: 3000
        });
        fetchSessions();
        fetchCurrentSession();
      } else {
        setError(data.error || 'Funkcja zatrzymania jest obecnie niedostępna');
        showToast({
          message: data.error || 'Funkcja zatrzymania jest obecnie niedostępna',
          type: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      setError('Wystąpił błąd podczas próby zatrzymania scrapowania');
      showToast({
        message: 'Wystąpił błąd podczas próby zatrzymania scrapowania',
        type: 'error',
        duration: 5000
      });
    }
  };

  // Nowa funkcja do czyszczenia historii scrapowań
  const handleClearHistory = async (keepCurrent: boolean = false) => {
    if (!window.confirm(
      keepCurrent 
        ? 'Czy na pewno chcesz usunąć całą historię scrapowań z wyjątkiem aktualnego scrapowania?' 
        : 'Czy na pewno chcesz usunąć CAŁĄ historię scrapowań, łącznie z aktualnym scrapowaniem?'
    )) {
      return;
    }

    try {
      setIsClearing(true);
      
      const url = new URL('/api/scrapings/clear', window.location.origin);
      if (keepCurrent) {
        url.searchParams.set('keepCurrent', 'true');
      }
      
      const response = await fetch(url.toString(), {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setSuccess(
          keepCurrent 
            ? 'Usunięto historię scrapowań (z wyjątkiem aktualnego)' 
            : 'Usunięto całą historię scrapowań'
        );
        showToast({
          message: keepCurrent 
            ? 'Usunięto historię scrapowań (z wyjątkiem aktualnego)' 
            : 'Usunięto całą historię scrapowań',
          type: 'success',
          duration: 3000
        });
        
        // Odświeżenie danych
        fetchSessions();
        if (!keepCurrent) {
          setCurrentSession(null);
        }
      } else {
        setError(data.error || 'Wystąpił błąd podczas usuwania historii scrapowań');
        showToast({
          message: data.error || 'Wystąpił błąd podczas usuwania historii scrapowań',
          type: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      setError('Wystąpił błąd podczas usuwania historii scrapowań');
      showToast({
        message: 'Wystąpił błąd podczas usuwania historii scrapowań',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsClearing(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, [pagination.page]);

  useEffect(() => {
    fetchCurrentSession();
    fetchCronStatus();
    
    // Automatyczne odświeżanie co 10 sekund jeśli istnieje aktywna sesja
    const intervalId = setInterval(() => {
      if (currentSession && currentSession.status === 'in_progress') {
        fetchCurrentSession();
        fetchCronStatus();
      }
    }, 10000);
    
    return () => clearInterval(intervalId);
  }, []);

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Historia scrapowań</h1>
        
        {/* Przyciski do uruchamiania/zatrzymywania scrapowania */}
        <div className="flex items-center space-x-2">
          {cronStatus?.isScrapingActive ? (
            <Button
              onClick={() => updateCronStatus(undefined, undefined, false, false, true)}
              disabled={updatingCron}
              variant="destructive"
              className="flex items-center"
            >
              <StopIcon className="mr-2 h-4 w-4" />
              Zatrzymaj scrapowanie
            </Button>
          ) : (
            <Button
              onClick={() => updateCronStatus(undefined, undefined, true, false, false)}
              disabled={updatingCron}
              variant="outline"
              className="flex items-center"
            >
              <ReloadIcon className="mr-2 h-4 w-4" />
              Uruchom scrapowanie ręczne
            </Button>
          )}
        </div>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
    
      
      {/* Aktywne scrapowanie */}
      {currentSession && (currentSession.status === 'in_progress' || currentSession.status === 'paused') && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Aktywne scrapowanie</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Aktualny postęp procesu scrapowania
            </p>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status</h3>
                <div className="flex flex-col space-y-2">
                  <StatusBadge status={currentSession.status} />
                  {currentSession.status === 'paused' && currentSession.pauseReason && (
                    <div className="text-sm text-yellow-600 dark:text-yellow-400">
                      {currentSession.pauseReason === '403_errors' && "Pauzowanie z powodu błędów 403"}
                      {currentSession.pauseReason === 'auto_pause_limit' && "Automatyczna pauza po 500 rekordach"}
                      {currentSession.pauseReason === 'manual_pause' && "Pauzowanie ręczne"}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Rozpoczęto</h3>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatDate(currentSession.startedAt)}
                </p>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg col-span-1 md:col-span-2">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">URL</h3>
                <p className="text-sm font-medium text-gray-900 dark:text-white break-all">
                  {currentSession.scrapingUrl}
                </p>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg col-span-1 md:col-span-2 flex justify-between items-center">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Akcje</h3>
                <div className="space-x-2">
                  {/* Tymczasowo ukryte przyciski
                  {currentSession.status === 'paused' && (
                    <Button 
                      onClick={() => handleResume(currentSession.id)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Wznów
                    </Button>
                  )}
                  
                  {currentSession.status === 'in_progress' && (
                    <Button 
                      onClick={() => handlePause(currentSession.id)}
                      className="bg-amber-500 hover:bg-amber-600"
                    >
                      <PauseIcon className="mr-2 h-4 w-4" />
                      Zatrzymaj
                    </Button>
                  )}
                  */}
                </div>
              </div>
            </div>
            
            {/* Komponent z paskami postępu odświeżany co 1 sekundę */}
            <ScrapingProgress />
          </div>
        </div>
      )}
      
      {/* Wyświetl komunikat o pauzie */}
      {currentSession && currentSession.status === 'paused' && currentSession.pausedAt && currentSession.pauseDuration && (
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <PauseIcon className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">Scrapowanie wstrzymane</h3>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-400">
                <p>
                  {currentSession.pauseReason === '403_errors' && "Scrapowanie zostało wstrzymane automatycznie z powodu zbyt wielu błędów 403."}
                  {currentSession.pauseReason === 'auto_pause_limit' && "Scrapowanie zostało wstrzymane automatycznie po osiągnięciu limitu 500 przetworzonych rekordów."}
                  {currentSession.pauseReason === 'manual_pause' && "Scrapowanie zostało wstrzymane ręcznie przez użytkownika."}
                  {!currentSession.pauseReason && "Scrapowanie zostało wstrzymane."}
                </p>
                <p className="mt-1">
                  Wstrzymano: {formatDate(currentSession.pausedAt)} na {currentSession.pauseDuration} sekund.
                </p>
                <p className="mt-2">
                  {/* Tymczasowo ukryty przycisk
                  <Button 
                    onClick={() => handleResume(currentSession.id)}
                    className="bg-green-600 hover:bg-green-700"
                    size="sm"
                  >
                    <PlayIcon className="mr-2 h-3 w-3" />
                    Wznów teraz
                  </Button>
                  */}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Historia scrapowań */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="flex  border-b border-gray-200 dark:border-gray-700">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Historia scrapowań</h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Lista wszystkich przeprowadzonych scrapowań
          </p>
        </div>

        <div className="ml-auto mr-6 mt-6">
        {/* Przyciski do czyszczenia historii */}
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleClearHistory(true)}
            disabled={isClearing || !sessions || sessions.length === 0}
            className="border-amber-300 dark:border-amber-700 text-amber-700 dark:text-amber-300 hover:bg-amber-50 dark:hover:bg-amber-900/30"
          >
            <TrashIcon className="mr-1 h-3.5 w-3.5" />
            Wyczyść historię (zostaw aktywne)
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleClearHistory(false)}
            disabled={isClearing || !sessions || sessions.length === 0}
            className="border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30"
          >
            <TrashIcon className="mr-1 h-3.5 w-3.5" />
            Wyczyść całą historię
          </Button>
          </div>
        </div>
        </div>

        
        
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie danych...</p>
          </div>
        ) : sessions.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            Brak sesji scrapowania
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Start
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Koniec
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Czas
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Strony
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    ID ogłoszeń
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Szczegóły
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Numery
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Akcje
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {sessions.map((session) => (
                  <tr key={session.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(session.startedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(session.finishedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDuration(session.startedAt, session.finishedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col">
                        <span>{session.currentPage}/{session.totalPages}</span>
                        <span className="mt-1 font-medium text-primary">
                          {formatPercent(session.pageProgress || Math.ceil(session.currentPage / Math.max(1, session.totalPages) * 100))}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col">
                        <span>{session.adsIdsScraped}/{session.totalAdsIds || '?'}</span>
                        <span className="mt-1 font-medium text-primary">
                          {formatPercent(session.adsIdsProgress || (session.totalAdsIds ? Math.ceil(session.adsIdsScraped / session.totalAdsIds * 100) : 0))}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col">
                        <span>{session.adsDetailsScraped}/{session.totalAdsIds || '?'}</span>
                        <span className="mt-1 font-medium text-primary">
                          {formatPercent(session.adsDetailsProgress || (session.totalAdsIds ? Math.ceil(session.adsDetailsScraped / session.totalAdsIds * 100) : 0))}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col">
                        <span>{session.adsWithPhones}/{session.totalAdsIds || '?'}</span>
                        <span className="mt-1 font-medium text-primary">
                          {formatPercent(session.adsWithPhonesProgress || (session.totalAdsIds ? Math.ceil(session.adsWithPhones / session.totalAdsIds * 100) : 0))}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col">
                        <StatusBadge status={session.status} />
                        {session.status === 'paused' && session.pauseReason && (
                          <span className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                            {session.pauseReason === '403_errors' && "Pauza: błędy 403"}
                            {session.pauseReason === 'auto_pause_limit' && "Pauza: limit 500"}
                            {session.pauseReason === 'manual_pause' && "Pauza ręczna"}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="space-x-2 flex">
                        {/* Tymczasowo ukryte przyciski 
                        {(session.status === 'interrupted' || session.status === 'paused') && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleResume(session.id)}
                            className="flex items-center"
                          >
                            <PlayIcon className="mr-1 h-3 w-3" />
                            Wznów
                          </Button>
                        )}
                        {session.status === 'in_progress' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handlePause(session.id)}
                            className="flex items-center bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-800 text-amber-800 dark:text-amber-300"
                          >
                            <PauseIcon className="mr-1 h-3 w-3" />
                            Zatrzymaj
                          </Button>
                        )}
                        */}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        {/* Paginacja */}
        {pagination.totalPages > 1 && !loading && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1 || loading}
              >
                Poprzednia
              </Button>
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Następna
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                  <span className="font-medium">
                    {Math.min(pagination.page * pagination.limit, pagination.total)}
                  </span>{' '}
                  z <span className="font-medium">{pagination.total}</span> sesji
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Pierwsza</span>
                    «
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Poprzednia</span>
                    ‹
                  </Button>
                  
                  {/* Numery stron */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pagination.page === pageNum ? "default" : "outline"}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pageNum)}
                        disabled={loading}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Następna</span>
                    ›
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.totalPages)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Ostatnia</span>
                    »
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
