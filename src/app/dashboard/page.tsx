"use client";

import Layout from "@/components/Layout";
import StatsCard from "@/components/Dashboard/StatsCard";
import ScrapingStatusCard from "@/components/Dashboard/ScrapingStatusCard";
import { 
  EnvelopeClosedIcon, 
  FileTextIcon, 
  DashboardIcon 
} from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { getFromApi } from "@/lib/utils/api";

export default function Dashboard() {
  const [ads, setAds] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [stats, setStats] = useState({
    adsScraped: 0,
    smsSent: 0,
    smsCost: "0.00",
    smsToday: 0,
    smsThisWeek: 0,
    smsThisMonth: 0
  });
  const [statsLoading, setStatsLoading] = useState<boolean>(false);
  
  // Pobieranie ostatnich 10 ogłoszeń
  const fetchLatestAds = async () => {
    try {
      setLoading(true);
      const response = await getFromApi('/api/ads?page=1&limit=10');
      if (response.ok) {
        const data = await response.json();
        setAds(data.ads || []);
      }
    } catch (error) {
      console.error("Błąd podczas pobierania ogłoszeń:", error);
    } finally {
      setLoading(false);
    }
  };
  
  // Pobieranie statystyk
  const fetchStats = async () => {
    try {
      setStatsLoading(true);
      const response = await getFromApi('/api/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Błąd podczas pobierania statystyk:", error);
    } finally {
      setStatsLoading(false);
    }
  };
  
  // Pobieranie danych przy pierwszym renderowaniu
  useEffect(() => {
    fetchLatestAds();
    fetchStats();
  }, []);
  
  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Dashboard</h1>
      
      {/* Status scrapowania */}
      <ScrapingStatusCard />
      
      {/* Statystyki */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-3 lg:grid-cols-3 mb-8">
        <StatsCard
          title="SMS-y wysłane (30 dni)"
          value={statsLoading ? "..." : stats.smsSent.toString()}
          icon={<EnvelopeClosedIcon className="h-6 w-6" />}
        />
        <StatsCard
          title="SMS-y dzisiaj"
          value={statsLoading ? "..." : stats.smsToday.toString()}
          icon={<EnvelopeClosedIcon className="h-6 w-6" />}
        />
        <StatsCard
          title="SMS-y w tym tygodniu"
          value={statsLoading ? "..." : stats.smsThisWeek.toString()}
          icon={<EnvelopeClosedIcon className="h-6 w-6" />}
        />
        <StatsCard
          title="SMS-y w tym miesiącu"
          value={statsLoading ? "..." : stats.smsThisMonth.toString()}
          icon={<EnvelopeClosedIcon className="h-6 w-6" />}
        />
        <StatsCard
          title="Ogłoszenia zescrapowane (30 dni)"
          value={statsLoading ? "..." : stats.adsScraped.toString()}
          icon={<FileTextIcon className="h-6 w-6" />}
        />
        <StatsCard
          title="Wydatki na SMS-y"
          value={statsLoading ? "..." : `${stats.smsCost.replace(".", ",")} zł`}
          icon={<DashboardIcon className="h-6 w-6" />}
        />
      </div>
      
      {/* Tabela ogłoszeń */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Ostatnie ogłoszenia</h2>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchStats}
              disabled={statsLoading}
            >
              {statsLoading ? 'Ładowanie...' : 'Odśwież statystyki'}
            </Button>
            <Button 
              variant="default" 
              size="sm" 
              onClick={fetchLatestAds}
              disabled={loading}
            >
              {loading ? 'Ładowanie...' : 'Odśwież ogłoszenia'}
            </Button>
          </div>
        </div>
        <div className="border-t border-gray-200 dark:border-gray-700">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Marka/Model
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Cena
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rok
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Przebieg
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status SMS
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Tel
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                      <div className="flex justify-center items-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <span className="ml-2">Ładowanie...</span>
                      </div>
                    </td>
                  </tr>
                ) : ads.length > 0 ? (
                  ads.map((ad) => (
                    <tr key={ad.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {ad.url ? (
                          <a
                            href={ad.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 dark:text-blue-400 hover:underline"
                          >
                            {ad.brandModel}
                          </a>
                        ) : (
                          ad.brandModel
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {ad.price}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {ad.year}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {ad.mileage}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs ${ad.smsSent ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
                          {ad.smsSent ? 'Wysłany' : 'Nie wysłany'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {ad.phones && ad.phones.length > 0 && (
                          <span className="text-sm">{ad.phones[0].number}</span>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                      Brak danych
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
}
