"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ChatBubbleIcon } from "@radix-ui/react-icons";
import { getFrom<PERSON>pi, delete<PERSON>rom<PERSON>pi, putTo<PERSON>pi } from "@/lib/utils/api";

interface Phone {
  id: number;
  number: string;
  label?: string;
}

interface Ad {
  id: number;
  externalId: string;
  brandModel: string;
  desc?: string;
  comment?: string;
  date?: string;
  location?: string;
  mileage?: string;
  year?: string;
  price?: string;
  url?: string;
  phones: Phone[];
  createdAt: string;
  updatedAt: string;
  smsSent: boolean;
  smsSentAt?: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function AdsPage() {
  const router = useRouter();
  const [ads, setAds] = useState<Ad[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [smsSentFilter, setSmsSentFilter] = useState<string | null>(null);
  const [noPhoneFilter, setNoPhoneFilter] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [currentAdId, setCurrentAdId] = useState<number | null>(null);
  const [commentText, setCommentText] = useState("");
  const [savingComment, setSavingComment] = useState(false);

  useEffect(() => {
    fetchAds();
  }, [pagination.page, search, sortBy, sortOrder, smsSentFilter, noPhoneFilter]);

  const fetchAds = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });
      
      if (search) {
        queryParams.append("search", search);
      }
      
      if (smsSentFilter !== null) {
        queryParams.append("smsSent", smsSentFilter);
      }
      
      if (noPhoneFilter !== null) {
        queryParams.append("noPhone", noPhoneFilter);
      }
      
      const response = await getFromApi(`/api/ads?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać ogłoszeń");
      }
      
      const data = await response.json();
      setAds(data.ads);
      setPagination(data.pagination);
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania ogłoszeń");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    setPagination({ ...pagination, page: 1 });
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  const handleDeleteAll = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await deleteFromApi("/api/ads", {
        body: JSON.stringify({}),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć ogłoszeń");
      }
      
      setAds([]);
      setPagination({ ...pagination, total: 0, totalPages: 0 });
      setSuccess("Wszystkie ogłoszenia zostały usunięte");
      setShowDeleteConfirm(false);
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania ogłoszeń");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenCommentDialog = (ad: Ad) => {
    setCurrentAdId(ad.id);
    setCommentText(ad.comment || "");
    setCommentDialogOpen(true);
  };

  const handleCloseCommentDialog = () => {
    setCommentDialogOpen(false);
    setCurrentAdId(null);
    setCommentText("");
  };

  const handleSaveComment = async () => {
    if (currentAdId === null) return;
    
    try {
      setSavingComment(true);
      setError(null);
      
      const response = await putToApi("/api/ads", {
        id: currentAdId,
        comment: commentText
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zapisać komentarza");
      }
      
      // Aktualizuj lokalną listę ogłoszeń
      setAds(ads.map(ad => 
        ad.id === currentAdId ? { ...ad, comment: commentText } : ad
      ));
      
      setSuccess("Komentarz został zapisany pomyślnie");
      handleCloseCommentDialog();
    } catch (error) {
      setError("Wystąpił błąd podczas zapisywania komentarza");
      console.error(error);
    } finally {
      setSavingComment(false);
    }
  };

  const handleDeleteAd = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await deleteFromApi("/api/ads", {
        body: JSON.stringify({ id }),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć ogłoszenia");
      }
      
      setAds(ads.filter(ad => ad.id !== id));
      setPagination({ ...pagination, total: pagination.total - 1 });
      setSuccess("Ogłoszenie zostało usunięte");
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania ogłoszenia");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Ogłoszenia</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex flex-wrap justify-between items-center">
          <div className="flex-1 min-w-0">
            <form onSubmit={handleSearch} className="flex w-full md:w-96 gap-2">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="Szukaj ogłoszeń..."
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              />
              <Button type="submit" className="rounded-md">
                Szukaj
              </Button>
            </form>
          </div>
          
          <div className="flex mt-4 md:mt-0 space-x-2">
            <div className="flex space-x-2">
              <select
                value={smsSentFilter === null ? "" : smsSentFilter}
                onChange={(e) => setSmsSentFilter(e.target.value === "" ? null : e.target.value)}
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md pr-8"
                style={{ paddingRight: '2rem' }}
              >
                <option value="">Wszystkie</option>
                <option value="true">SMS wysłany</option>
                <option value="false">SMS nie wysłany</option>
              </select>
              
              <select
                value={noPhoneFilter === null ? "" : noPhoneFilter}
                onChange={(e) => setNoPhoneFilter(e.target.value === "" ? null : e.target.value)}
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md pr-8"
                style={{ paddingRight: '2rem', display: 'none' }}
              >
                <option value="">Wszystkie</option>
                <option value="true">Bez numeru tel</option>
                <option value="false">Z numerem tel</option>
              </select>
            </div>
            
            <Button
              variant="destructive"
              onClick={() => setShowDeleteConfirm(true)}
              disabled={loading || ads.length === 0}
            >
              Usuń wszystkie
            </Button>
          </div>
        </div>
        
        {showDeleteConfirm && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border-b border-gray-200 dark:border-gray-700">
            <p className="text-red-700 dark:text-red-400 mb-4">
              Czy na pewno chcesz usunąć wszystkie ogłoszenia? Tej operacji nie można cofnąć.
            </p>
            <div className="flex space-x-2">
              <Button
                variant="destructive"
                onClick={handleDeleteAll}
                disabled={loading}
              >
                Tak, usuń wszystkie
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={loading}
              >
                Anuluj
              </Button>
            </div>
          </div>
        )}
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("brandModel")}
                >
                  <div className="flex items-center">
                    Marka/Model
                    {sortBy === "brandModel" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("price")}
                >
                  <div className="flex items-center">
                    Cena
                    {sortBy === "price" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("year")}
                >
                  <div className="flex items-center">
                    Rok
                    {sortBy === "year" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("mileage")}
                >
                  <div className="flex items-center">
                    Przebieg
                    {sortBy === "mileage" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("location")}
                >
                  <div className="flex items-center">
                    Lokalizacja
                    {sortBy === "location" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("smsSent")}
                >
                  <div className="flex items-center">
                    Status SMS
                    {sortBy === "smsSent" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort("createdAt")}
                  >
                    <div className="flex items-center">
                      Data dodania
                      {sortBy === "createdAt" && (
                        <span className="ml-1">
                          {sortOrder === "asc" ? "↑" : "↓"}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                   Tel
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Komentarz
                  </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Akcje
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                      <span className="ml-2">Ładowanie...</span>
                    </div>
                  </td>
                </tr>
              ) : ads.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                    Brak ogłoszeń
                  </td>
                </tr>
              ) : (
                ads.map((ad) => (
                  <tr key={ad.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {ad.url ? (
                        <a
                          href={ad.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:underline"
                        >
                          {ad.brandModel}
                        </a>
                      ) : (
                        ad.brandModel
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.price}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.year}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.mileage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.location || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {ad.phones.length === 0 ? (
                        <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          Brak tel.
                        </span>
                      ) : ad.smsSent ? (
                        <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                          Wysłany
                        </span>
                      ) : (
                        <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                          Nie wysłany
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(ad.createdAt).toLocaleString('pl-PL')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                               {ad.phones && ad.phones.length > 0 && (
                          <span className="text-sm">{ad.phones[0].number}</span>
                        )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <button
                        onClick={() => handleOpenCommentDialog(ad)}
                        className="focus:outline-none"
                        title={ad.comment ? ad.comment : "Dodaj komentarz"}
                      >
                        <ChatBubbleIcon 
                          className={`h-5 w-5 ${ad.comment 
                            ? 'text-green-900 dark:text-white font-bold' 
                            : 'text-gray-300 dark:text-gray-600'}`} 
             
                        />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <Button
                        variant="link"
                        size="sm"
                        className="text-red-600 dark:text-red-400"
                        onClick={() => {
                          if (window.confirm(`Czy na pewno chcesz usunąć ogłoszenie "${ad.brandModel}"?`)) {
                            handleDeleteAd(ad.id);
                          }
                        }}
                      >
                        Usuń
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {/* Paginacja */}
        {pagination.totalPages > 0 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1 || loading}
              >
                Poprzednia
              </Button>
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Następna
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                  <span className="font-medium">
                    {Math.min(pagination.page * pagination.limit, pagination.total)}
                  </span>{' '}
                  z <span className="font-medium">{pagination.total}</span> ogłoszeń
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Pierwsza</span>
                    «
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Poprzednia</span>
                    ‹
                  </Button>
                  
                  {/* Numery stron */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pagination.page === pageNum ? "default" : "outline"}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pageNum)}
                        disabled={loading}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Następna</span>
                    ›
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.totalPages)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Ostatnia</span>
                    »
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Dialog do edycji komentarza */}
      {commentDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Komentarz do ogłoszenia
            </h3>
            <textarea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={4}
              placeholder="Wpisz komentarz..."
            />
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={handleCloseCommentDialog}
                disabled={savingComment}
              >
                Anuluj
              </Button>
              <Button
                onClick={handleSaveComment}
                disabled={savingComment}
              >
                {savingComment ? "Zapisywanie..." : "Zapisz"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
