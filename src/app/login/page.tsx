"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError("Email i hasło są wymagane");
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Błąd logowania");
      }
      
      const data = await response.json();
      
      try {
        // Zapisz token w localStorage dla użycia w żądaniach API
        localStorage.setItem("token", data.token);
        
        // Zapisz również token w ciasteczku dla JavaScript
        document.cookie = `auth_token=${data.token}; path=/; max-age=${60 * 60 * 24}; SameSite=Strict`;
        
        // Sprawdź, czy token został poprawnie zapisany
        const savedToken = localStorage.getItem("token");
        if (!savedToken) {
          throw new Error("Nie udało się zapisać tokenu uwierzytelniającego");
        }
        
        console.log("Zalogowano pomyślnie, token:", data.token.substring(0, 20) + "...");
        
        // Dodaj nagłówek Authorization do przyszłych żądań
        if (typeof window !== 'undefined') {
          // Ustaw domyślny nagłówek dla fetch
          const originalFetch = window.fetch;
          window.fetch = function(url, options = {}) {
            const modifiedOptions = { ...options };
            modifiedOptions.headers = {
              ...(modifiedOptions.headers || {}),
              'Authorization': `Bearer ${data.token}`
            };
            return originalFetch(url, modifiedOptions);
          };
        }
        
        // Przekieruj na stronę dashboard z małym opóźnieniem
        setTimeout(() => {
          // Pełne odświeżenie strony, aby zastosować nowe nagłówki i ciasteczka
          window.location.href = "/dashboard?token=" + encodeURIComponent(data.token);
        }, 500);
      } catch (storageError) {
        console.error("Błąd podczas zapisywania tokenu:", storageError);
        setError("Wystąpił problem z zapisaniem danych logowania. Spróbuj ponownie lub sprawdź ustawienia przeglądarki.");
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "Wystąpił błąd podczas logowania");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Funkcja do czyszczenia ciasteczek i localStorage
  const clearAuth = () => {
    try {
      // Usuń token z localStorage
      localStorage.removeItem("token");
      
      // Usuń ciasteczka związane z uwierzytelnianiem
      document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
      document.cookie = "auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
      
      setError(null);
      alert("Ciasteczka i dane logowania zostały wyczyszczone. Możesz teraz spróbować zalogować się ponownie.");
    } catch (error) {
      console.error("Błąd podczas czyszczenia danych logowania:", error);
      setError("Wystąpił problem z czyszczeniem danych logowania.");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-10 bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
            OtoScraper
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
            Zaloguj się, aby kontynuować
          </p>
        </div>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div className="mb-2">
              <label htmlFor="email" className="sr-only">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm dark:bg-gray-700"
                placeholder="Email"
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Hasło
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm dark:bg-gray-700"
                placeholder="Hasło"
              />
            </div>
          </div>

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              {loading ? "Logowanie..." : "Zaloguj się"}
            </Button>
          </div>
        </form>
        
        <div className="pt-4 text-center">
          <button
            type="button"
            onClick={clearAuth}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            Problemy z logowaniem? Wyczyść dane logowania
          </button>
        </div>
      </div>
    </div>
  );
}
