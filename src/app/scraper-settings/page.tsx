"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { LockClosedIcon } from "@radix-ui/react-icons";

interface ScraperSettings {
  id: number;
  scraperType: string;
  headless: boolean;
  slowMo: number;
  userAgentRotation: boolean;
  proxyEnabled: boolean;
  proxyUrl?: string;
  maxPages: number;
  turboMode?: boolean;
  allowedRegions?: string;
  updatedAt: string;
}

export default function ScraperSettingsPage() {
  const [settings, setSettings] = useState<ScraperSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Dodaj nowe stany dla modali potwierdzenia
  const [showDeleteAdsConfirm, setShowDeleteAdsConfirm] = useState(false);
  const [showDeletePhonesConfirm, setShowDeletePhonesConfirm] = useState(false);
  const [deletingAds, setDeletingAds] = useState(false);
  const [deletingPhones, setDeletingPhones] = useState(false);

  // Funkcje do obsługi usuwania
  const handleDeleteAllAds = async () => {
    try {
      setDeletingAds(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch("/api/ads/clear-all", {
        method: "DELETE",
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć ogłoszeń");
      }
      
      const data = await response.json();
      setSuccess(`Usunięto wszystkie ogłoszenia (${data.count})`);
      setShowDeleteAdsConfirm(false);
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania ogłoszeń");
      console.error(error);
    } finally {
      setDeletingAds(false);
    }
  };

  const handleDeleteAllPhones = async () => {
    try {
      setDeletingPhones(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch("/api/phones/clear-all", {
        method: "DELETE",
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć numerów telefonów");
      }
      
      const data = await response.json();
      setSuccess(`Usunięto wszystkie numery telefonów (${data.count})`);
      setShowDeletePhonesConfirm(false);
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania numerów telefonów");
      console.error(error);
    } finally {
      setDeletingPhones(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/scraper-settings");
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać ustawień scrapera");
      }
      
      const data = await response.json();
      
      // Wymuszenie zablokowanych ustawień niezależnie od wartości z serwera
      setSettings({
        ...data,
        scraperType: "puppeteer",
        headless: false,
        userAgentRotation: true,
        proxyEnabled: false,
        turboMode: true
      });
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania ustawień scrapera");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (!settings) return;

    const { name, value, type } = e.target;
    
    setSettings({
      ...settings,
      [name]: type === "number" ? parseInt(value) : value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!settings) return;

    const { name, checked } = e.target;
    
    setSettings({
      ...settings,
      [name]: checked,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!settings) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    // Wymuszenie zablokowanych ustawień przed wysłaniem
    const settingsToSave = {
      ...settings,
      scraperType: "puppeteer",
      headless: false,
      userAgentRotation: true,
      proxyEnabled: false,
      turboMode: true
    };
    
    try {
      const response = await fetch("/api/scraper-settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settingsToSave),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zapisać ustawień scrapera");
      }
      
      const updatedSettings = await response.json();
      // Ustawiamy zablokowane wartości także w stanie komponentu
      setSettings({
        ...updatedSettings,
        scraperType: "puppeteer",
        headless: false,
        userAgentRotation: true,
        proxyEnabled: false,
        turboMode: true
      });
      setSuccess("Ustawienia scrapera zostały zapisane pomyślnie");
    } catch (error) {
      setError("Wystąpił błąd podczas zapisywania ustawień scrapera");
      console.error(error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie ustawień scrapera...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Ustawienia scrapera</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      {settings && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Zaawansowana konfiguracja scrapera</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Dostosuj ustawienia scrapera do swoich potrzeb
            </p>
          </div>
          
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-6">
              <div>
                <label htmlFor="scraperType" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Typ scrapera
                  <span className="ml-2 text-xs text-gray-500 dark:text-gray-500 cursor-help inline-flex items-center" title="Opcja zablokowana przez administratora">
                    <LockClosedIcon className="h-3 w-3" />
                  </span>
                </label>
                <select
                  id="scraperType"
                  name="scraperType"
                  value="puppeteer"
                  disabled
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white bg-gray-100 dark:bg-gray-800 cursor-not-allowed focus:outline-none sm:text-sm rounded-md"
                >
                  <option value="puppeteer">Puppeteer (JavaScript)</option>
                </select>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Puppeteer jest wymaganym typem scrapera i nie może być zmieniony
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="headless"
                  id="headless"
                  checked={false}
                  disabled
                  className="h-4 w-4 text-blue-400 border-gray-300 rounded cursor-not-allowed"
                />
                <label htmlFor="headless" className="ml-2 block text-sm text-gray-500 dark:text-gray-500">
                  Tryb headless (bez interfejsu graficznego)
                  <span className="ml-2 text-xs text-gray-500 dark:text-gray-500 cursor-help inline-flex items-center" title="Opcja zablokowana przez administratora">
                    <LockClosedIcon className="h-3 w-3" />
                  </span>
                </label>
              </div>
              
              <div>
                <label htmlFor="slowMo" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Opóźnienie (ms)
                </label>
                <input
                  type="number"
                  name="slowMo"
                  id="slowMo"
                  value={settings.slowMo}
                  onChange={handleChange}
                  min="0"
                  max="1000"
                  className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Opóźnienie między akcjami scrapera (w milisekundach)
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="userAgentRotation"
                  id="userAgentRotation"
                  checked={true}
                  disabled
                  className="h-4 w-4 text-blue-400 border-gray-300 rounded cursor-not-allowed"
                />
                <label htmlFor="userAgentRotation" className="ml-2 block text-sm text-gray-500 dark:text-gray-500">
                  Rotacja User-Agent
                  <span className="ml-2 text-xs text-gray-500 dark:text-gray-500 cursor-help inline-flex items-center" title="Opcja zablokowana przez administratora">
                    <LockClosedIcon className="h-3 w-3" />
                  </span>
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="proxyEnabled"
                  id="proxyEnabled"
                  checked={false}
                  disabled
                  className="h-4 w-4 text-blue-400 border-gray-300 rounded cursor-not-allowed"
                />
                <label htmlFor="proxyEnabled" className="ml-2 block text-sm text-gray-500 dark:text-gray-500">
                  Używaj proxy
                  <span className="ml-2 text-xs text-gray-500 dark:text-gray-500 cursor-help inline-flex items-center" title="Opcja zablokowana przez administratora">
                    <LockClosedIcon className="h-3 w-3" />
                  </span>
                </label>
              </div>
              
              {settings.proxyEnabled && (
                <div>
                  <label htmlFor="proxyUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    URL proxy
                  </label>
                  <input
                    type="text"
                    name="proxyUrl"
                    id="proxyUrl"
                    value={settings.proxyUrl || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    placeholder="http://username:<EMAIL>:8080"
                  />
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    URL serwera proxy (format: *****************************:port)
                  </p>
                </div>
              )}
              
              <div>
                <label htmlFor="maxPages" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Maksymalna liczba stron
                </label>
                <input
                  type="number"
                  name="maxPages"
                  id="maxPages"
                  value={settings.maxPages}
                  onChange={handleChange}
                  min="-1"
                  className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Maksymalna liczba stron do scrapowania. Wartość -1 oznacza wszystkie strony.
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="turboMode"
                  id="turboMode"
                  checked={true}
                  disabled
                  className="h-4 w-4 text-blue-400 border-gray-300 rounded cursor-not-allowed"
                />
                <label htmlFor="turboMode" className="ml-2 block text-sm text-gray-500 dark:text-gray-500">
                  Inteligentny tryb turbo (eksperymentalne)
                </label>
                <div className="ml-2 px-2 py-1 text-xs text-white bg-gray-500 rounded">Nowe</div>
                <span className="ml-2 text-xs text-gray-500 dark:text-gray-500 cursor-help inline-flex items-center" title="Opcja zablokowana przez administratora">
                  <LockClosedIcon className="h-3 w-3" />
                </span>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Dozwolone województwa
                </label>
                <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
                  {[
                    "Mazowieckie", 
                    "Łódzkie", 
                    "Lubelskie", 
                    "Świętokrzyskie", 
                    "Podlaskie",
                    "Małopolskie",
                    "Śląskie",
                    "Wielkopolskie",
                    "Pomorskie",
                    "Zachodniopomorskie",
                    "Dolnośląskie",
                    "Kujawsko-Pomorskie",
                    "Warmińsko-Mazurskie",
                    "Lubuskie",
                    "Opolskie",
                    "Podkarpackie"
                  ].map((region) => {
                    // Sprawdź czy region jest w liście dozwolonych
                    const allowedRegions = settings.allowedRegions 
                      ? JSON.parse(settings.allowedRegions) 
                      : ["Mazowieckie", "Łódzkie", "Lubelskie", "Świętokrzyskie", "Podlaskie"];
                    
                    const isChecked = allowedRegions.includes(region);
                    
                    return (
                      <div key={region} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`region-${region}`}
                          checked={isChecked}
                          onChange={(e) => {
                            // Aktualizuj listę województw
                            const currentAllowedRegions = settings.allowedRegions 
                              ? JSON.parse(settings.allowedRegions) 
                              : ["Mazowieckie", "Łódzkie", "Lubelskie", "Świętokrzyskie", "Podlaskie"];
                            
                            let newAllowedRegions;
                            if (e.target.checked) {
                              // Dodaj do listy
                              newAllowedRegions = [...currentAllowedRegions, region];
                            } else {
                              // Usuń z listy
                              newAllowedRegions = currentAllowedRegions.filter((r: string) => r !== region);
                            }
                            
                            // Aktualizuj stan
                            setSettings({
                              ...settings,
                              allowedRegions: JSON.stringify(newAllowedRegions)
                            });
                          }}
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                        />
                        <label htmlFor={`region-${region}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          {region}
                        </label>
                      </div>
                    );
                  })}
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Zaznacz województwa, z których chcesz pobierać ogłoszenia. 
                  Ogłoszenia z innych województw zostaną zignorowane.
                </p>
              </div>
              
              {/* Usunięto pole cookieAcceptSelector - teraz jest przechowywane w selektorach */}
              
              <div className="pt-5">
                <div className="flex justify-between">
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => setShowDeleteAdsConfirm(true)}
                      disabled={saving || deletingAds || deletingPhones}
                    >
                      Usuń wszystkie ogłoszenia
                    </Button>
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => setShowDeletePhonesConfirm(true)}
                      disabled={saving || deletingAds || deletingPhones}
                    >
                      Usuń wszystkie numery telefonów
                    </Button>
                  </div>
                  <Button
                    type="submit"
                    disabled={saving || deletingAds || deletingPhones}
                  >
                    {saving ? "Zapisywanie..." : "Zapisz ustawienia"}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </div>
      )}
    </Layout>
  );

  {/* Dodaj modalne okna potwierdzenia po formularzu */}
  {showDeleteAdsConfirm && (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Potwierdź usunięcie</h3>
        <p className="text-red-700 dark:text-red-400 mb-4">
          Czy na pewno chcesz usunąć wszystkie ogłoszenia? Tej operacji nie można cofnąć.
        </p>
        <div className="flex space-x-2 justify-end">
          <Button
            variant="destructive"
            onClick={handleDeleteAllAds}
            disabled={deletingAds}
          >
            {deletingAds ? "Usuwanie..." : "Tak, usuń wszystkie"}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowDeleteAdsConfirm(false)}
            disabled={deletingAds}
          >
            Anuluj
          </Button>
        </div>
      </div>
    </div>
  )}

  {showDeletePhonesConfirm && (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Potwierdź usunięcie</h3>
        <p className="text-red-700 dark:text-red-400 mb-4">
          Czy na pewno chcesz usunąć wszystkie numery telefonów? Tej operacji nie można cofnąć.
        </p>
        <div className="flex space-x-2 justify-end">
          <Button
            variant="destructive"
            onClick={handleDeleteAllPhones}
            disabled={deletingPhones}
          >
            {deletingPhones ? "Usuwanie..." : "Tak, usuń wszystkie"}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowDeletePhonesConfirm(false)}
            disabled={deletingPhones}
          >
            Anuluj
          </Button>
        </div>
      </div>
    </div>
  )}
}
