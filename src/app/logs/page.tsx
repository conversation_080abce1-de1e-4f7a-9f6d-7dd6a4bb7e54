"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";

interface LogEntry {
  timestamp: string;
  message: string;
  level: "info" | "warn" | "error";
}

export default function LogsPage() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/logs");
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać logów");
      }
      
      const data = await response.json();
      setLogs(data.logs);
      setError(null);
    } catch (error) {
      console.error("Błąd podczas pobierania logów:", error);
      setError("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas pobierania logów");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  useEffect(() => {
    let refreshInterval: NodeJS.Timeout;
    
    if (autoRefresh) {
      refreshInterval = setInterval(() => {
        fetchLogs();
      }, 5000); // Odświeżaj co 5 sekund
    }
    
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh]);

  const getLogLevelClass = (level: string) => {
    switch (level) {
      case "error":
        return "text-red-500 dark:text-red-400";
      case "warn":
        return "text-yellow-500 dark:text-yellow-400";
      case "info":
      default:
        return "text-blue-500 dark:text-blue-400";
    }
  };

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Logi Scrapera</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <Button onClick={fetchLogs} disabled={loading}>
            {loading ? "Odświeżanie..." : "Odśwież"}
          </Button>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoRefresh"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="autoRefresh" className="text-sm text-gray-700 dark:text-gray-300">
              Auto-odświeżanie (co 5s)
            </label>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => window.open("/api/logs?download=true", "_blank")}
          >
            Pobierz plik logów
          </Button>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Ostatnie 30 wiadomości (najnowsze na górze)
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        {loading && logs.length === 0 ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-3">Ładowanie logów...</span>
          </div>
        ) : logs.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Czas
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Poziom
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Wiadomość
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {logs.map((log, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(log.timestamp).toLocaleString('pl-PL')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={getLogLevelClass(log.level)}>
                        {log.level.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 break-words">
                      {log.message}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
            Brak logów do wyświetlenia
          </div>
        )}
      </div>
    </Layout>
  );
}
