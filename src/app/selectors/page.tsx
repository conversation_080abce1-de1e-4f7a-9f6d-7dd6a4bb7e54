"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";

interface Selector {
  id: number;
  name: string;
  cssSelector: string;
  xpathSelector: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function SelectorsPage() {
  const [selectors, setSelectors] = useState<Selector[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchSelectors();
  }, []);

  const fetchSelectors = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/selectors");
      
      if (!response.ok) {
        throw new Error("<PERSON>e udało si<PERSON> pobra<PERSON> selektorów");
      }
      
      const data = await response.json();
      setSelectors(data);
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania selektorów");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectorChange = (id: number, field: keyof Selector, value: string | boolean) => {
    setSelectors(selectors.map(selector => 
      selector.id === id ? { ...selector, [field]: value } : selector
    ));
  };

  const handleSaveSelector = async (id: number) => {
    try {
      setSaving(id);
      setError(null);
      setSuccess(null);
      
      const selectorToUpdate = selectors.find(s => s.id === id);
      
      if (!selectorToUpdate) {
        throw new Error("Nie znaleziono selektora");
      }
      
      const response = await fetch("/api/selectors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(selectorToUpdate),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zapisać selektora");
      }
      
      const updatedSelector = await response.json();
      
      setSelectors(selectors.map(selector => 
        selector.id === id ? updatedSelector : selector
      ));
      
      setSuccess(`Selektor "${updatedSelector.name}" został zapisany pomyślnie`);
    } catch (error) {
      setError("Wystąpił błąd podczas zapisywania selektora");
      console.error(error);
    } finally {
      setSaving(null);
    }
  };

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Mapowanie selektorów</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Selektory</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Dostosuj selektory używane do scrapowania danych z OtoMoto
            </p>
          </div>
        </div>
        
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie selektorów...</p>
          </div>
        ) : selectors.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            Brak skonfigurowanych selektorów
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {selectors.map((selector) => (
              <div key={selector.id} className="p-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor={`name-${selector.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Nazwa
                    </label>
                    <input
                      type="text"
                      id={`name-${selector.id}`}
                      value={selector.name}
                      onChange={(e) => handleSelectorChange(selector.id, "name", e.target.value)}
                      className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor={`description-${selector.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Opis
                    </label>
                    <input
                      type="text"
                      id={`description-${selector.id}`}
                      value={selector.description || ""}
                      onChange={(e) => handleSelectorChange(selector.id, "description", e.target.value)}
                      className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor={`css-selector-${selector.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Selektor CSS
                    </label>
                    <input
                      type="text"
                      id={`css-selector-${selector.id}`}
                      value={selector.cssSelector}
                      onChange={(e) => handleSelectorChange(selector.id, "cssSelector", e.target.value)}
                      className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor={`xpath-selector-${selector.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Selektor XPath
                    </label>
                    <input
                      type="text"
                      id={`xpath-selector-${selector.id}`}
                      value={selector.xpathSelector}
                      onChange={(e) => handleSelectorChange(selector.id, "xpathSelector", e.target.value)}
                      className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    />
                  </div>
                  
                  <div className="sm:col-span-2 flex justify-end">
                    <Button
                      onClick={() => handleSaveSelector(selector.id)}
                      disabled={saving !== null}
                    >
                      {saving === selector.id ? "Zapisywanie..." : "Zapisz zmiany"}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
}
