"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ChatBubbleIcon, PlusIcon } from "@radix-ui/react-icons";
import { getFromApi, deleteFrom<PERSON><PERSON>, putTo<PERSON>pi, postToApi } from "@/lib/utils/api";

interface LeaseReminder {
  id: number;
  status: string;
  fullName: string;
  phone: string;
  email?: string;
  carDetails: string;
  note?: string;
  expirationDate: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function LeaseRemindersPage() {
  const router = useRouter();
  const [reminders, setReminders] = useState<LeaseReminder[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [sortBy, setSortBy] = useState("expirationDate");
  const [sortOrder, setSortOrder] = useState("asc");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [currentReminderId, setCurrentReminderId] = useState<number | null>(null);
  const [noteText, setNoteText] = useState("");
  const [savingNote, setSavingNote] = useState(false);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<LeaseReminder>>({
    status: "potencjalnie",
    fullName: "",
    phone: "",
    email: "",
    carDetails: "",
    note: "",
    expirationDate: "",
  });
  const [isEditing, setIsEditing] = useState(false);
  const [savingForm, setSavingForm] = useState(false);

  useEffect(() => {
    fetchReminders();
  }, [pagination.page, search, sortBy, sortOrder]);

  const fetchReminders = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });
      
      if (search) {
        queryParams.append("search", search);
      }
      
      const response = await getFromApi(`/api/lease-reminders?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać przypomnień");
      }
      
      const data = await response.json();
      setReminders(data.reminders);
      setPagination(data.pagination);
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania przypomnień");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    setPagination({ ...pagination, page: 1 });
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  const handleDeleteAll = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Usuń wszystkie przypomnienia
      const promises = reminders.map(reminder => 
        deleteFromApi("/api/lease-reminders", {
          body: JSON.stringify({ id: reminder.id }),
        })
      );
      
      await Promise.all(promises);
      
      setReminders([]);
      setPagination({ ...pagination, total: 0, totalPages: 0 });
      setSuccess("Wszystkie przypomnienia zostały usunięte");
      setShowDeleteConfirm(false);
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania przypomnień");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenNoteDialog = (reminder: LeaseReminder) => {
    setCurrentReminderId(reminder.id);
    setNoteText(reminder.note || "");
    setNoteDialogOpen(true);
  };

  const handleCloseNoteDialog = () => {
    setNoteDialogOpen(false);
    setCurrentReminderId(null);
    setNoteText("");
  };

  const handleSaveNote = async () => {
    if (currentReminderId === null) return;
    
    try {
      setSavingNote(true);
      setError(null);
      
      const response = await putToApi("/api/lease-reminders", {
        id: currentReminderId,
        note: noteText
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zapisać notatki");
      }
      
      // Aktualizuj lokalną listę przypomnień
      setReminders(reminders.map(reminder => 
        reminder.id === currentReminderId ? { ...reminder, note: noteText } : reminder
      ));
      
      setSuccess("Notatka została zapisana pomyślnie");
      handleCloseNoteDialog();
    } catch (error) {
      setError("Wystąpił błąd podczas zapisywania notatki");
      console.error(error);
    } finally {
      setSavingNote(false);
    }
  };

  const handleDeleteReminder = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await deleteFromApi("/api/lease-reminders", {
        body: JSON.stringify({ id }),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć przypomnienia");
      }
      
      setReminders(reminders.filter(reminder => reminder.id !== id));
      setPagination({ ...pagination, total: pagination.total - 1 });
      setSuccess("Przypomnienie zostało usunięte");
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania przypomnienia");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenFormDialog = (reminder?: LeaseReminder) => {
    if (reminder) {
      // Edycja istniejącego przypomnienia
      setIsEditing(true);
      setFormData({
        id: reminder.id,
        status: reminder.status,
        fullName: reminder.fullName,
        phone: reminder.phone,
        email: reminder.email || "",
        carDetails: reminder.carDetails,
        note: reminder.note || "",
        expirationDate: new Date(reminder.expirationDate).toISOString().split('T')[0],
      });
    } else {
      // Dodawanie nowego przypomnienia
      setIsEditing(false);
      setFormData({
        status: "potencjalnie",
        fullName: "",
        phone: "",
        email: "",
        carDetails: "",
        note: "",
        expirationDate: "",
      });
    }
    setFormDialogOpen(true);
  };

  const handleCloseFormDialog = () => {
    setFormDialogOpen(false);
    setFormData({
      status: "potencjalnie",
      fullName: "",
      phone: "",
      email: "",
      carDetails: "",
      note: "",
      expirationDate: "",
    });
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveForm = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSavingForm(true);
      setError(null);
      
      if (!formData.fullName || !formData.phone || !formData.carDetails || !formData.expirationDate) {
        setError("Wypełnij wszystkie wymagane pola");
        setSavingForm(false);
        return;
      }
      
      let response;
      
      if (isEditing) {
        // Aktualizacja istniejącego przypomnienia
        response = await putToApi("/api/lease-reminders", formData);
      } else {
        // Dodawanie nowego przypomnienia
        response = await postToApi("/api/lease-reminders", formData);
      }
      
      if (!response.ok) {
        throw new Error(`Nie udało się ${isEditing ? 'zaktualizować' : 'dodać'} przypomnienia`);
      }
      
      const savedReminder = await response.json();
      
      if (isEditing) {
        // Aktualizuj lokalną listę przypomnień
        setReminders(reminders.map(reminder => 
          reminder.id === savedReminder.id ? savedReminder : reminder
        ));
      } else {
        // Dodaj nowe przypomnienie do listy
        setReminders([...reminders, savedReminder]);
        setPagination(prev => ({ ...prev, total: prev.total + 1 }));
      }
      
      setSuccess(`Przypomnienie zostało ${isEditing ? 'zaktualizowane' : 'dodane'} pomyślnie`);
      handleCloseFormDialog();
      fetchReminders(); // Odśwież listę
    } catch (error) {
      setError(`Wystąpił błąd podczas ${isEditing ? 'aktualizacji' : 'dodawania'} przypomnienia`);
      console.error(error);
    } finally {
      setSavingForm(false);
    }
  };

  // Funkcja obliczająca liczbę dni do wygaśnięcia leasingu
  const calculateDaysRemaining = (expirationDate: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const expDate = new Date(expirationDate);
    expDate.setHours(0, 0, 0, 0);
    
    const diffTime = expDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  // Funkcja zwracająca klasę CSS dla liczby dni
  const getDaysClass = (days: number) => {
    if (days < 0) return "text-gray-500 dark:text-gray-400"; // Przeterminowane
    if (days <= 30) return "text-red-600 dark:text-red-400"; // Mniej niż 30 dni
    if (days <= 90) return "text-yellow-600 dark:text-yellow-400"; // Mniej niż 90 dni
    return "text-green-600 dark:text-green-400"; // Ponad 90 dni
  };

  // Funkcja zwracająca klasę CSS dla statusu
  const getStatusClass = (status: string) => {
    switch (status) {
      case "niezainteresowany":
        return "text-white bg-red-600"; // czerwony
      case "potencjalnie":
        return "text-white bg-gray-500"; // szary
      case "zainteresowany":
        return "text-white bg-green-600"; // zielony
      case "zamkniety":
        return "text-white bg-green-800"; // ciemnozielony
      default:
        return "text-white bg-gray-500"; // domyślnie szary
    }
  };

  // Funkcja zwracająca etykietę dla statusu
  const getStatusLabel = (status: string) => {
    switch (status) {
      case "niezainteresowany":
        return "Niezainteresowany";
      case "potencjalnie":
        return "Potencjalnie";
      case "zainteresowany":
        return "Zainteresowany";
      case "zamkniety":
        return "Zamknięty";
      default:
        return status;
    }
  };

  return (
    <Layout>
      <h1 className="mb-6 text-2xl font-semibold text-gray-900 dark:text-white">Przypomnienia o leasingu</h1>
      
      {error && (
        <div className="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
          {error}
        </div>
      )}
      
      {success && (
        <div className="px-4 py-3 mb-4 text-green-700 bg-green-100 border border-green-400 rounded">
          {success}
        </div>
      )}
      
      <div className="overflow-hidden bg-white rounded-lg shadow dark:bg-gray-800">
        <div className="flex flex-wrap items-center justify-between px-4 py-5 border-b border-gray-200 sm:px-6 dark:border-gray-700">
          <div className="flex-1 min-w-0">
            <form onSubmit={handleSearch} className="flex w-full gap-2 md:w-96">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="Szukaj przypomnień..."
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
              <Button type="submit" className="rounded-md">
                Szukaj
              </Button>
            </form>
          </div>
          
          <div className="flex mt-4 space-x-2 md:mt-0">
            <Button
              onClick={() => handleOpenFormDialog()}
              className="text-white bg-green-600 hover:bg-green-700"
            >
              <PlusIcon className="w-4 h-4 mr-1" />
              Dodaj nowe
            </Button>
            
            <Button
              variant="destructive"
              onClick={() => setShowDeleteConfirm(true)}
              disabled={loading || reminders.length === 0}
            >
              Usuń wszystkie
            </Button>
          </div>
        </div>
        
        {showDeleteConfirm && (
          <div className="p-4 border-b border-gray-200 bg-red-50 dark:bg-red-900/20 dark:border-gray-700">
            <p className="mb-4 text-red-700 dark:text-red-400">
              Czy na pewno chcesz usunąć wszystkie przypomnienia? Tej operacji nie można cofnąć.
            </p>
            <div className="flex space-x-2">
              <Button
                variant="destructive"
                onClick={handleDeleteAll}
                disabled={loading}
              >
                Tak, usuń wszystkie
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={loading}
              >
                Anuluj
              </Button>
            </div>
          </div>
        )}
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer dark:text-gray-300"
                  onClick={() => handleSort("status")}
                >
                  <div className="flex items-center">
                    Status
                    {sortBy === "status" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer dark:text-gray-300"
                  onClick={() => handleSort("fullName")}
                >
                  <div className="flex items-center">
                    Imię i nazwisko
                    {sortBy === "fullName" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer dark:text-gray-300"
                  onClick={() => handleSort("phone")}
                >
                  <div className="flex items-center">
                    Telefon
                    {sortBy === "phone" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer dark:text-gray-300"
                  onClick={() => handleSort("email")}
                >
                  <div className="flex items-center">
                    Email
                    {sortBy === "email" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer dark:text-gray-300"
                  onClick={() => handleSort("carDetails")}
                >
                  <div className="flex items-center">
                    Samochód
                    {sortBy === "carDetails" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase cursor-pointer dark:text-gray-300"
                  onClick={() => handleSort("expirationDate")}
                >
                  <div className="flex items-center">
                    Data wygaśnięcia
                    {sortBy === "expirationDate" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300"
                >
                  Pozostało dni
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase dark:text-gray-300"
                >
                  Notatka
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300"
                >
                  Akcje
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap dark:text-gray-400">
                    <div className="flex items-center justify-center">
                      <div className="w-6 h-6 border-b-2 border-blue-500 rounded-full animate-spin"></div>
                      <span className="ml-2">Ładowanie...</span>
                    </div>
                  </td>
                </tr>
              ) : reminders.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap dark:text-gray-400">
                    Brak przypomnień
                  </td>
                </tr>
              ) : (
                reminders.map((reminder) => {
                  const daysRemaining = calculateDaysRemaining(reminder.expirationDate);
                  const daysClass = getDaysClass(daysRemaining);
                  
                  return (
                    <tr key={reminder.id}>
                      <td className="px-6 py-4 text-sm whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusClass(reminder.status)}`}>
                          {getStatusLabel(reminder.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap dark:text-white">
                        {reminder.fullName}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap dark:text-gray-400">
                        {reminder.phone}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap dark:text-gray-400">
                        {reminder.email || "-"}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap dark:text-gray-400">
                        {reminder.carDetails}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap dark:text-gray-400">
                        {new Date(reminder.expirationDate).toLocaleDateString('pl-PL')}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${daysClass}`}>
                        {daysRemaining < 0 
                          ? `Przeterminowane (${Math.abs(daysRemaining)} dni temu)` 
                          : `${daysRemaining} dni`}
                      </td>
                      <td className="px-6 py-4 text-sm text-center whitespace-nowrap">
                        <button
                          onClick={() => handleOpenNoteDialog(reminder)}
                          className="focus:outline-none"
                          title={reminder.note ? reminder.note : "Dodaj notatkę"}
                        >
                          <ChatBubbleIcon 
                            className={`h-5 w-5 ${reminder.note 
                              ? 'text-green-900 dark:text-white font-bold' 
                              : 'text-gray-300 dark:text-gray-600'}`} 
                          />
                        </button>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap dark:text-gray-400">
                        <div className="flex space-x-2">
                          <Button
                            variant="link"
                            size="sm"
                            className="text-blue-600 dark:text-blue-400"
                            onClick={() => handleOpenFormDialog(reminder)}
                          >
                            Edytuj
                          </Button>
                          <Button
                            variant="link"
                            size="sm"
                            className="text-red-600 dark:text-red-400"
                            onClick={() => {
                              if (window.confirm(`Czy na pewno chcesz usunąć przypomnienie dla "${reminder.fullName}"?`)) {
                                handleDeleteReminder(reminder.id);
                              }
                            }}
                          >
                            Usuń
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
        
        {/* Paginacja */}
        {pagination.totalPages > 0 && (
          <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex justify-between flex-1 sm:hidden">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1 || loading}
              >
                Poprzednia
              </Button>
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Następna
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                  <span className="font-medium">
                    {Math.min(pagination.page * pagination.limit, pagination.total)}
                  </span>{' '}
                  z <span className="font-medium">{pagination.total}</span> przypomnień
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 rounded-l-md dark:border-gray-600"
                    onClick={() => handlePageChange(1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Pierwsza</span>
                    «
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Poprzednia</span>
                    ‹
                  </Button>
                  
                  {/* Numery stron */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pagination.page === pageNum ? "default" : "outline"}
                        className="relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600"
                        onClick={() => handlePageChange(pageNum)}
                        disabled={loading}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Następna</span>
                    ›
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 text-sm font-medium border border-gray-300 rounded-r-md dark:border-gray-600"
                    onClick={() => handlePageChange(pagination.totalPages)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Ostatnia</span>
                    »
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Dialog do edycji notatki */}
      {noteDialogOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-lg dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Notatka do przypomnienia
            </h3>
            <textarea
              value={noteText}
              onChange={(e) => setNoteText(e.target.value)}
              className="w-full p-2 text-gray-900 bg-white border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              rows={4}
              placeholder="Wpisz notatkę..."
            />
            <div className="flex justify-end mt-4 space-x-2">
              <Button
                variant="outline"
                onClick={handleCloseNoteDialog}
                disabled={savingNote}
              >
                Anuluj
              </Button>
              <Button
                onClick={handleSaveNote}
                disabled={savingNote}
              >
                {savingNote ? "Zapisywanie..." : "Zapisz"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Dialog formularza dodawania/edycji */}
      {formDialogOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-lg dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              {isEditing ? "Edytuj przypomnienie" : "Dodaj nowe przypomnienie"}
            </h3>
            <form onSubmit={handleSaveForm}>
              <div className="space-y-4">
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status *
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleFormChange}
                    required
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="niezainteresowany">Niezainteresowany</option>
                    <option value="potencjalnie">Potencjalnie</option>
                    <option value="zainteresowany">Zainteresowany</option>
                    <option value="zamkniety">Zamknięty</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Imię i nazwisko *
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleFormChange}
                    required
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Telefon *
                  </label>
                  <input
                    type="text"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleFormChange}
                    required
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleFormChange}
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label htmlFor="carDetails" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Samochód *
                  </label>
                  <input
                    type="text"
                    id="carDetails"
                    name="carDetails"
                    value={formData.carDetails}
                    onChange={handleFormChange}
                    required
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Data wygaśnięcia leasingu *
                  </label>
                  <input
                    type="date"
                    id="expirationDate"
                    name="expirationDate"
                    value={formData.expirationDate}
                    onChange={handleFormChange}
                    required
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label htmlFor="note" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Notatka
                  </label>
                  <textarea
                    id="note"
                    name="note"
                    value={formData.note}
                    onChange={handleFormChange}
                    rows={3}
                    className="block w-full px-3 py-2 mt-1 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div className="flex justify-end mt-6 space-x-2">
                <Button
                  variant="outline"
                  onClick={handleCloseFormDialog}
                  disabled={savingForm}
                  type="button"
                >
                  Anuluj
                </Button>
                <Button
                  type="submit"
                  disabled={savingForm}
                >
                  {savingForm ? "Zapisywanie..." : isEditing ? "Zapisz zmiany" : "Dodaj przypomnienie"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Layout>
  );
}
