"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { PlayIcon, StopIcon, ReloadIcon } from "@radix-ui/react-icons";
import { useToast } from "@/components/ui/toast";

interface Settings {
  id: number;
  smsContent: string;
  scrapingUrl: string;
  cronEnabled: boolean;
  scrapingInterval: number;
  smsPrice: number;
  smsResendDays: number;
  updatedAt: string;
  lastScrapedAt?: string;
}

interface CronStatus {
  enabled: boolean;
  interval: number;
  lastScrapedAt: string | null;
  isActive: boolean;
  currentScrapingPage: number | null;
  isScrapingActive: boolean;
  error?: string;
}

export default function SettingsPage() {
  const router = useRouter();
  const { showToast } = useToast();
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [cronStatus, setCronStatus] = useState<CronStatus | null>(null);
  const [loadingCronStatus, setLoadingCronStatus] = useState(false);
  const [updatingCron, setUpdatingCron] = useState(false);

  // Pobierz status crona
  const fetchCronStatus = async () => {
    try {
      setLoadingCronStatus(true);
      const response = await fetch("/api/cron");
      
      if (response.ok) {
        const data = await response.json();
        setCronStatus(data);
      } else {
        console.error("Nie udało się pobrać statusu crona");
      }
    } catch (error) {
      console.error("Błąd podczas pobierania statusu crona:", error);
    } finally {
      setLoadingCronStatus(false);
    }
  };

  // Aktualizuj status crona
  const updateCronStatus = async (enabled?: boolean, interval?: number, runNow?: boolean, cleanupSmsHistory?: boolean, stopScraping?: boolean) => {
    try {
      setUpdatingCron(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch("/api/cron", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          enabled,
          interval,
          runNow,
          cleanupSmsHistory,
          stopScraping
        }),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zaktualizować statusu crona");
      }
      
      const data = await response.json();
      setCronStatus(data.status);
      
      if (enabled !== undefined) {
        setSettings(prev => prev ? { ...prev, cronEnabled: enabled } : null);
      }
      
      if (interval !== undefined) {
        setSettings(prev => prev ? { ...prev, scrapingInterval: interval } : null);
      }
      
      let successMessage = "Status crona zaktualizowany pomyślnie";
      if (runNow) {
        successMessage = "Scrapowanie uruchomione ręcznie";
      } else if (stopScraping) {
        successMessage = "Scrapowanie zatrzymane";
        setSuccess(successMessage);
      } else if (cleanupSmsHistory) {
        const deletedCount = data.status.deletedSmsHistoryCount || 0;
        successMessage = `Usunięto ${deletedCount} starych wpisów z historii SMS-ów`;
        // Pokaż toast dla czyszczenia karencji SMS
        showToast({
          message: successMessage,
          type: "success",
          duration: 5000
        });
      } else {
        setSuccess(successMessage);
      }
    } catch (error) {
      setError("Wystąpił błąd podczas aktualizacji statusu crona");
      console.error(error);
    } finally {
      setUpdatingCron(false);
    }
  };

  useEffect(() => {
    // Pobierz ustawienia z API
    const fetchSettings = async () => {
      try {
        const response = await fetch("/api/settings");
        if (!response.ok) {
          throw new Error("Nie udało się pobrać ustawień");
        }
        const data = await response.json();
        setSettings(data);
      } catch (error) {
        setError("Wystąpił błąd podczas pobierania ustawień");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
    fetchCronStatus();
    
    // Odświeżaj status crona co 10 sekund
    const interval = setInterval(fetchCronStatus, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!settings) return;

    const { name, value, type } = e.target;
    
    setSettings({
      ...settings,
      [name]: type === "number" ? parseFloat(value) : value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!settings) return;

    const { name, checked } = e.target;
    
    setSettings({
      ...settings,
      [name]: checked,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!settings) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await fetch("/api/settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zapisać ustawień");
      }
      
      const updatedSettings = await response.json();
      setSettings(updatedSettings);
      setSuccess("Ustawienia zostały zapisane pomyślnie");
      
      // Odśwież stronę po 2 sekundach
      setTimeout(() => {
        router.refresh();
      }, 2000);
    } catch (error) {
      setError("Wystąpił błąd podczas zapisywania ustawień");
      console.error(error);
    } finally {
      setSaving(false);
    }
  };

  const handleStartScraping = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await fetch("/api/scraper", {
        method: "POST",
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się uruchomić scrapera");
      }
      
      const result = await response.json();
      setSuccess(`Scrapowanie zakończone pomyślnie. Pobrano ${result.count} ogłoszeń.`);
    } catch (error) {
      setError("Wystąpił błąd podczas uruchamiania scrapera");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie ustawień...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Ustawienia ogólne</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      {settings && (
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Aplikacja</h2>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Skonfiguruj podstawowe ustawienia aplikacji
              </p>
            </div>
          
            <form onSubmit={handleSubmit} className="p-6">
              <div className="space-y-6">
              <div>
                <label htmlFor="scrapingUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  URL do scrapowania
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="scrapingUrl"
                    id="scrapingUrl"
                    value={settings.scrapingUrl}
                    onChange={handleChange}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    placeholder="https://www.otomoto.pl/osobowe/..."
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Pełny URL do strony OtoMoto, z której będą pobierane ogłoszenia
                </p>
              </div>
              
              <div>
                <label htmlFor="smsContent" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Treść SMS
                </label>
                <div className="mt-1">
                  <textarea
                    name="smsContent"
                    id="smsContent"
                    rows={3}
                    value={settings.smsContent}
                    onChange={handleChange}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                    placeholder="Treść wiadomości SMS"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Treść wiadomości SMS, która będzie wysyłana do sprzedających
                </p>
              </div>
              
              <div>
                <label htmlFor="smsPrice" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Cena SMS (zł)
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="smsPrice"
                    id="smsPrice"
                    value={settings.smsPrice}
                    onChange={handleChange}
                    step="0.01"
                    min="0"
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Cena jednego SMS-a (do obliczania kosztów)
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="cronEnabled"
                  id="cronEnabled"
                  checked={settings.cronEnabled}
                  onChange={handleCheckboxChange}
                  className="hidden h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="cronEnabled" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Włącz automatyczne scrapowanie
                </label>
              </div>
              
              <div>
                <label htmlFor="scrapingInterval" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Interwał scrapowania (minuty)
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="scrapingInterval"
                    id="scrapingInterval"
                    value={settings.scrapingInterval}
                    onChange={handleChange}
                    min="1"
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Jak często (w minutach) ma być uruchamiane automatyczne scrapowanie
                </p>
              </div>
              
              <div>
                <label htmlFor="smsResendDays" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Okres karencji SMS (dni)
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="smsResendDays"
                    id="smsResendDays"
                    value={settings.smsResendDays}
                    onChange={handleChange}
                    min="1"
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Liczba dni, po których można ponownie wysłać SMS do tego samego numeru
                </p>
              </div>
              
                <div className="flex justify-between pt-4">
              
                  
                  <Button
                    type="submit"
                    disabled={loading || saving}
                  >
                    {saving ? "Zapisywanie..." : "Zapisz ustawienia"}
                  </Button>
                </div>
              </div>
            </form>
          </div>
          
          {/* Sekcja statusu crona */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Status scrapowania</h2>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Zarządzaj scrapowaniem ręcznym i automatycznym
              </p>
            </div>
            
            <div className="p-6">
              {loadingCronStatus ? (
                <div className="flex justify-center items-center h-24">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <span className="ml-3">Ładowanie statusu...</span>
                </div>
              ) : cronStatus ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status crona (automat)</h3>
                      <div className="flex items-center">
                        <div className={`h-3 w-3 rounded-full mr-2 ${cronStatus.isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <span className="text-lg font-medium text-gray-900 dark:text-white">
                          {cronStatus.isActive ? 'Aktywny' : 'Nieaktywny'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Interwał</h3>
                      <span className="text-lg font-medium text-gray-900 dark:text-white">
                        {cronStatus.interval} minut
                      </span>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Ostatnie scrapowanie</h3>
                      <span className="text-lg font-medium text-gray-900 dark:text-white">
                        {cronStatus.lastScrapedAt ? new Date(cronStatus.lastScrapedAt).toLocaleString('pl-PL') : 'Nigdy'}
                      </span>
                    </div>
                    
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status scrapowania</h3>
                      <div className="flex items-center">
                        <div className={`h-3 w-3 rounded-full mr-2 ${cronStatus.isScrapingActive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                        <span className="text-lg font-medium text-gray-900 dark:text-white">
                          {cronStatus.isScrapingActive 
                            ? `Aktywne (strona ${cronStatus.currentScrapingPage || '?'})` 
                            : 'Nieaktywne'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-3 mt-6">
                    <Button
                      onClick={() => updateCronStatus(true, undefined, false, false)}
                      disabled={updatingCron || cronStatus.isActive}
                      className="flex items-center"
                    >
                      <PlayIcon className="mr-2 h-4 w-4" />
                      Uruchom cron
                    </Button>
                    
                    <Button
                      onClick={() => updateCronStatus(false, undefined, false, false)}
                      disabled={updatingCron || !cronStatus.isActive}
                      variant="destructive"
                      className="flex items-center"
                    >
                      <StopIcon className="mr-2 h-4 w-4" />
                      Zatrzymaj cron
                    </Button>
                    
                    {cronStatus.isScrapingActive ? (
                      <Button
                        onClick={() => updateCronStatus(undefined, undefined, false, false, true)}
                        disabled={updatingCron}
                        variant="destructive"
                        className="flex items-center"
                      >
                        <StopIcon className="mr-2 h-4 w-4" />
                        Zatrzymaj scrapowanie
                      </Button>
                    ) : (
                      <Button
                        onClick={() => updateCronStatus(undefined, undefined, true, false, false)}
                        disabled={updatingCron}
                        variant="outline"
                        className="flex items-center"
                      >
                        <ReloadIcon className="mr-2 h-4 w-4" />
                        Uruchom scrapowanie ręczne
                      </Button>
                    )}
                    
                    <Button
                      onClick={() => updateCronStatus(undefined, undefined, false, true)}
                      disabled={updatingCron}
                      variant="outline"
                      className="flex items-center"
                    >
                      <ReloadIcon className="mr-2 h-4 w-4" />
                      Wyczyść karencję SMS
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 dark:text-gray-400 py-6">
                  <p>Nie można pobrać statusu crona. Upewnij się, że serwer jest uruchomiony z plikiem server.js.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
