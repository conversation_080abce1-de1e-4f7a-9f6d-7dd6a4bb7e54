"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { PlayIcon, GearIcon, ReloadIcon, ClockIcon, InfoCircledIcon, CheckCircledIcon, CrossCircledIcon } from "@radix-ui/react-icons";
import { useToast } from "@/components/ui/toast";
import { getFromApi, postToApi } from "@/lib/utils/api";
import Link from "next/link";

interface Ad {
  id: number;
  externalId: string;
  brandModel: string;
  desc?: string;
  comment?: string;
  date?: string;
  location?: string;
  mileage?: string;
  year?: string;
  price?: string;
  url?: string;
  phones: Phone[];
  createdAt: string;
  updatedAt: string;
  smsSent: boolean;
  smsSentAt?: string;
}

interface Phone {
  id: number;
  number: string;
  label?: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface ScrapingSession {
  id: number;
  startedAt: string;
  finishedAt: string | null;
  totalPages: number;
  currentPage: number;
  status: string;
  adsIdsScraped: number;
  adsDetailsScraped: number;
  adsWithPhones: number;
  totalAdsIds: number;
  scrapingUrl: string;
  createdAt: string;
  updatedAt: string;
  pageProgress?: number;
  adsIdsProgress?: number;
  adsDetailsProgress?: number;
  adsWithPhonesProgress?: number;
}

interface Settings {
  id: number;
  scrapingUrl: string;
  smsContent: string;
  cronEnabled: boolean;
  scrapingInterval: number;
  smsPrice: number;
  smsResendDays: number;
  lastScrapedAt?: string;
  updatedAt: string;
}

const formatDate = (dateString: string | null) => {
  if (!dateString) return "—";
  const date = new Date(dateString);
  return date.toLocaleString('pl');
};

const StatusBadge = ({ status }: { status: string }) => {
  if (status === 'in_progress') {
    return (
      <div className="flex items-center">
        <ClockIcon className="text-amber-500 mr-1" />
        <span className="text-amber-500 font-medium">W trakcie</span>
      </div>
    );
  } else if (status === 'completed') {
    return (
      <div className="flex items-center">
        <CheckCircledIcon className="text-green-500 mr-1" />
        <span className="text-green-500 font-medium">Zakończono</span>
      </div>
    );
  } else if (status === 'interrupted') {
    return (
      <div className="flex items-center">
        <CrossCircledIcon className="text-red-500 mr-1" />
        <span className="text-red-500 font-medium">Przerwano</span>
      </div>
    );
  } else {
    return (
      <div className="flex items-center">
        <InfoCircledIcon className="text-blue-500 mr-1" />
        <span className="text-blue-500 font-medium">{status}</span>
      </div>
    );
  }
};

export default function ScraperPage() {
  const router = useRouter();
  const { showToast } = useToast();
  const [ads, setAds] = useState<Ad[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 25, // 25 na stronę zgodnie z wymaganiami
    total: 0,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [currentSession, setCurrentSession] = useState<ScrapingSession | null>(null);
  const [settings, setSettings] = useState<Settings | null>(null);
  const [scrapingInProgress, setScrapingInProgress] = useState(false);

  useEffect(() => {
    fetchAds();
    fetchCurrentSession();
    fetchSettings();
    
    // Automatyczne odświeżanie co 10 sekund jeśli istnieje aktywna sesja
    const intervalId = setInterval(() => {
      if (currentSession && currentSession.status === 'in_progress') {
        fetchCurrentSession();
      }
    }, 10000);
    
    return () => clearInterval(intervalId);
  }, [pagination.page, search, sortBy, sortOrder]);

  const fetchAds = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });
      
      if (search) {
        queryParams.append("search", search);
      }
      
      const response = await getFromApi(`/api/ads?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać ogłoszeń");
      }
      
      const data = await response.json();
      setAds(data.ads);
      setPagination(data.pagination);
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania ogłoszeń");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrentSession = async () => {
    try {
      const response = await getFromApi('/api/scrapings/current');
      
      if (response.ok) {
        const data = await response.json();
        if (data && data.id) {
          setCurrentSession(data);
          setScrapingInProgress(data.status === 'in_progress');
        } else {
          setCurrentSession(null);
          setScrapingInProgress(false);
        }
      }
    } catch (error) {
      console.error('Błąd podczas pobierania aktualnej sesji scrapowania:', error);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await getFromApi('/api/settings');
      
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Błąd podczas pobierania ustawień:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    setPagination({ ...pagination, page: 1 });
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  const handleStartScraping = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await postToApi("/api/scraper", {});
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nie udało się uruchomić scrapera");
      }
      
      const result = await response.json();
      
      if (result.success) {
        setSuccess(`Scrapowanie zakończone pomyślnie. ${result.message}`);
        showToast({
          message: `Scrapowanie zakończone pomyślnie. ${result.message}`,
          type: "success",
          duration: 5000
        });
        
        // Odśwież dane
        fetchAds();
        fetchCurrentSession();
      } else {
        setError(`Błąd podczas scrapowania: ${result.message}`);
        showToast({
          message: `Błąd podczas scrapowania: ${result.message}`,
          type: "error",
          duration: 5000
        });
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "Wystąpił błąd podczas uruchamiania scrapera");
      showToast({
        message: error instanceof Error ? error.message : "Wystąpił błąd podczas uruchamiania scrapera",
        type: "error",
        duration: 5000
      });
    } finally {
      setLoading(false);
      // Odśwież status sesji
      fetchCurrentSession();
    }
  };

  const handleStopScraping = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await postToApi("/api/cron", {
        stopScraping: true
      });
      
      if (!response.ok) {
        throw new Error("Nie udało się zatrzymać scrapowania");
      }
      
      setSuccess("Scrapowanie zostało zatrzymane");
      showToast({
        message: "Scrapowanie zostało zatrzymane",
        type: "success",
        duration: 3000
      });
      
      // Odśwież status sesji
      fetchCurrentSession();
    } catch (error) {
      setError("Wystąpił błąd podczas zatrzymywania scrapowania");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Scrapowanie ogłoszeń</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      {/* Kafelek z ustawieniami */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Ustawienia scrapowania</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Konfiguracja parametrów scrapera
            </p>
          </div>
          <Link href="/settings">
            <Button variant="outline" className="flex items-center">
              <GearIcon className="mr-2 h-4 w-4" />
              Ustawienia ogólne
            </Button>
          </Link>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">URL do scrapowania</h3>
              <p className="text-sm font-medium text-gray-900 dark:text-white break-all">
                {settings?.scrapingUrl || "Nie skonfigurowano"}
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Ostatnie scrapowanie</h3>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {settings?.lastScrapedAt ? formatDate(settings.lastScrapedAt) : "Nigdy"}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Aktywne scrapowanie */}
      {currentSession && currentSession.status === 'in_progress' && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Aktywne scrapowanie</h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Aktualny postęp procesu scrapowania
            </p>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Status</h3>
                <StatusBadge status={currentSession.status} />
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Rozpoczęto</h3>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatDate(currentSession.startedAt)}
                </p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Strony</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {currentSession.currentPage} / {currentSession.totalPages}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${currentSession.pageProgress || 0}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">ID ogłoszeń</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {currentSession.adsIdsScraped} / {currentSession.totalAdsIds || '?'}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${currentSession.adsIdsProgress || 0}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Szczegóły ogłoszeń</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {currentSession.adsDetailsScraped} / {currentSession.totalAdsIds || '?'}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${currentSession.adsDetailsProgress || 0}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Numery telefonów</span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {currentSession.adsWithPhones} / {currentSession.totalAdsIds || '?'}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${currentSession.adsWithPhonesProgress || 0}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <Button
                variant="destructive"
                onClick={handleStopScraping}
                disabled={loading}
                className="flex items-center"
              >
                <CrossCircledIcon className="mr-2 h-4 w-4" />
                Zatrzymaj scrapowanie
              </Button>
            </div>
          </div>
        </div>
      )}
      
      {/* Lista ogłoszeń */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex flex-wrap justify-between items-center">
          <div className="flex-1 min-w-0">
            <form onSubmit={handleSearch} className="flex w-full md:w-96 gap-2">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="Szukaj ogłoszeń..."
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              />
              <Button type="submit" className="rounded-md">
                Szukaj
              </Button>
            </form>
          </div>
          
          <div className="mt-4 md:mt-0">
            <Button
              onClick={handleStartScraping}
              disabled={loading || scrapingInProgress}
              className="flex items-center"
            >
              {loading ? (
                <>
                  <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                  Uruchamianie...
                </>
              ) : (
                <>
                  <PlayIcon className="mr-2 h-4 w-4" />
                  Uruchom scrapowanie
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("brandModel")}
                >
                  <div className="flex items-center">
                    Marka/Model
                    {sortBy === "brandModel" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("price")}
                >
                  <div className="flex items-center">
                    Cena
                    {sortBy === "price" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("year")}
                >
                  <div className="flex items-center">
                    Rok
                    {sortBy === "year" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("mileage")}
                >
                  <div className="flex items-center">
                    Przebieg
                    {sortBy === "mileage" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("smsSent")}
                >
                  <div className="flex items-center">
                    Status SMS
                    {sortBy === "smsSent" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("createdAt")}
                >
                  <div className="flex items-center">
                    Data dodania
                    {sortBy === "createdAt" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Tel
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                      <span className="ml-2">Ładowanie...</span>
                    </div>
                  </td>
                </tr>
              ) : ads.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                    Brak ogłoszeń
                  </td>
                </tr>
              ) : (
                ads.map((ad) => (
                  <tr key={ad.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {ad.url ? (
                        <a
                          href={ad.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:underline"
                        >
                          {ad.brandModel}
                        </a>
                      ) : (
                        ad.brandModel
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.price}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.year}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.mileage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {ad.phones.length === 0 ? (
                        <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          Brak tel.
                        </span>
                      ) : ad.smsSent ? (
                        <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                          Wysłany
                        </span>
                      ) : (
                        <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                          Nie wysłany
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(ad.createdAt).toLocaleString('pl-PL')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {ad.phones && ad.phones.length > 0 && (
                        <span className="text-sm">{ad.phones[0].number}</span>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {/* Paginacja */}
        {pagination.totalPages > 0 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1 || loading}
              >
                Poprzednia
              </Button>
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Następna
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                  <span className="font-medium">
                    {Math.min(pagination.page * pagination.limit, pagination.total)}
                  </span>{' '}
                  z <span className="font-medium">{pagination.total}</span> ogłoszeń
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Pierwsza</span>
                    «
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <span className="sr-only">Poprzednia</span>
                    ‹
                  </Button>
                  
                  {/* Numery stron */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pagination.page === pageNum ? "default" : "outline"}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pageNum)}
                        disabled={loading}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Następna</span>
                    ›
                  </Button>
                  <Button
                    variant="outline"
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                    onClick={() => handlePageChange(pagination.totalPages)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    <span className="sr-only">Ostatnia</span>
                    »
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
