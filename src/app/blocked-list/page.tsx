"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { getFromApi, postToApi, deleteFromApi } from "@/lib/utils/api";

interface BlockedPhone {
  id: number;
  number: string;
  reason?: string;
  createdAt: string;
}

export default function BlockedListPage() {
  const [phones, setPhones] = useState<BlockedPhone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newPhone, setNewPhone] = useState("");
  const [reason, setReason] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25, // Zmieniono z 10 na 25 zgodnie z wymaganiami
    total: 0,
    totalPages: 0,
  });
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    fetchPhones();
  }, [pagination.page, search]);

  const fetchPhones = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (search) {
        queryParams.append("search", search);
      }
      
      const response = await getFromApi(`/api/blocked-phones?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się pobrać listy zablokowanych numerów");
      }
      
      const data = await response.json();
      setPhones(data.phones);
      setPagination(data.pagination);
    } catch (error) {
      setError("Wystąpił błąd podczas pobierania listy zablokowanych numerów");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setPagination({ ...pagination, page: newPage });
  };

  const handleAddPhone = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPhone) return;
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Walidacja numeru telefonu
      const phoneRegex = /^[0-9]{9}$/;
      if (!phoneRegex.test(newPhone)) {
        setError("Nieprawidłowy format numeru telefonu. Wprowadź 9 cyfr bez spacji i znaków specjalnych.");
        setLoading(false);
        return;
      }
      
      const response = await postToApi("/api/blocked-phones", { 
        number: newPhone,
        reason: reason || undefined
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Nie udało się dodać zablokowanego numeru");
      }
      
      const addedPhone = await response.json();
      
      // Odśwież listę numerów
      await fetchPhones();
      
      setSuccess(`Numer ${addedPhone.number} został dodany do listy zablokowanych`);
      setNewPhone("");
      setReason("");
    } catch (error) {
      setError(error instanceof Error ? error.message : "Wystąpił błąd podczas dodawania zablokowanego numeru");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePhone = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await deleteFromApi(`/api/blocked-phones?id=${id}`);
      
      if (!response.ok) {
        throw new Error("Nie udało się usunąć zablokowanego numeru");
      }
      
      setPhones(phones.filter(phone => phone.id !== id));
      setSuccess("Numer został usunięty z listy zablokowanych");
    } catch (error) {
      setError("Wystąpił błąd podczas usuwania zablokowanego numeru");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAll = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await postToApi("/api/blocked-phones/clear", {});
      
      if (!response.ok) {
        throw new Error("Nie udało się wyczyścić listy zablokowanych numerów");
      }
      
      setPhones([]);
      setPagination({ ...pagination, total: 0, totalPages: 0 });
      setSuccess("Lista zablokowanych numerów została wyczyszczona pomyślnie");
      setShowDeleteConfirm(false);
    } catch (error) {
      setError("Wystąpił błąd podczas czyszczenia listy zablokowanych numerów");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Lista zablokowanych numerów</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Zablokowane numery telefonów</h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Lista numerów telefonów, na które nie będą wysyłane SMS-y
          </p>
        </div>
        
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <form onSubmit={handleAddPhone} className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="text"
                value={newPhone}
                onChange={(e) => setNewPhone(e.target.value)}
                placeholder="Numer telefonu (9 cyfr)"
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                maxLength={9}
              />
              <Button
                type="submit"
                disabled={loading || !newPhone}
              >
                Dodaj do zablokowanych
              </Button>
            </div>
            <div>
              <input
                type="text"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Powód blokady (opcjonalnie)"
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              />
            </div>
          </form>
        </div>
        
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex flex-wrap justify-between items-center">
          <div className="flex-1 min-w-0">
            <form onSubmit={handleSearch} className="flex w-full md:w-96 gap-2">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="Szukaj numerów..."
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
              />
              <Button type="submit" className="rounded-md">
                Szukaj
              </Button>
            </form>
          </div>
          
          <div className="mt-4 md:mt-0">
            <Button
              variant="destructive"
              onClick={() => setShowDeleteConfirm(true)}
              disabled={loading || pagination.total === 0}
            >
              Wyczyść listę
            </Button>
          </div>
        </div>
        
        {showDeleteConfirm && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border-b border-gray-200 dark:border-gray-700">
            <p className="text-red-700 dark:text-red-400 mb-4">
              Czy na pewno chcesz usunąć wszystkie zablokowane numery? Tej operacji nie można cofnąć.
            </p>
            <div className="flex space-x-2">
              <Button
                variant="destructive"
                onClick={handleDeleteAll}
                disabled={loading}
              >
                Tak, usuń wszystkie
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={loading}
              >
                Anuluj
              </Button>
            </div>
          </div>
        )}
        
        {loading && !phones.length ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Ładowanie numerów...</p>
          </div>
        ) : phones.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            Brak zablokowanych numerów
          </div>
        ) : (
          <>
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {phones.map((phone) => (
                <li key={phone.id} className="px-6 py-4 flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{phone.number}</p>
                    {phone.reason && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Powód: {phone.reason}
                      </p>
                    )}
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Dodano: {new Date(phone.createdAt).toLocaleString('pl-PL')}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeletePhone(phone.id)}
                    disabled={loading}
                    className="text-red-600 border-red-600 hover:bg-red-50 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-900/20"
                  >
                    Usuń
                  </Button>
                </li>
              ))}
            </ul>
            
            {/* Paginacja */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    Poprzednia
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    Następna
                  </Button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Pokazuje <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> do{' '}
                      <span className="font-medium">
                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                      </span>{' '}
                      z <span className="font-medium">{pagination.total}</span> numerów
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <span className="sr-only">Pierwsza</span>
                        «
                      </Button>
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <span className="sr-only">Poprzednia</span>
                        ‹
                      </Button>
                      
                      {/* Numery stron */}
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum;
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1;
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i;
                        } else {
                          pageNum = pagination.page - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNum}
                            variant={pagination.page === pageNum ? "default" : "outline"}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={loading}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                      
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        <span className="sr-only">Następna</span>
                        ›
                      </Button>
                      <Button
                        variant="outline"
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium"
                        onClick={() => handlePageChange(pagination.totalPages)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        <span className="sr-only">Ostatnia</span>
                        »
                      </Button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
}
