// Globalne typy dla aplikacji

// Deklaracja typu dla global.cronFunctions
declare global {
  var cronFunctions: {
    initCron: () => Promise<void>;
    getCronStatus: () => Promise<any>;
    updateScrapingStatus: (page: number) => void;
    runScraper: () => Promise<void>;
    stopScraper: () => Promise<boolean>;
    runCleanupSentSmsHistory: () => Promise<number>;
    runSendLeaseRemindersEmail: () => Promise<any>;
    setScraperBrowser: (browser: any) => void;
    resumeScrapingSession: (sessionId: string | number) => Promise<any>;
    getCurrentScrapedAdIds: () => Promise<string[]>;
    addScrapedAdIds: (adIds: string[]) => number;
    setScrapedAdIds: (adIds: string[]) => number;
    clearScrapedAdIds: () => number;
  };
}

export {};
