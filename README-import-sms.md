# Skrypt importowania numerów telefonów z oznaczeniem wysłania SMS

Skrypt `import-sms-sent.js` importuje numery telefonów z pliku tekstowego lub JSON i wykonuje następujące operacje:

1. Znajduje ogłoszenia powiązane z tymi numerami w bazie danych
2. Oznacza te ogłoszenia jako "SMS wysłany" (ustawia flagę `smsSent` na `true`)
3. Dodaje numery do historii wysłanych SMS-ów (wprowadza je w okres karencji)
4. Usuwa je z listy do wysłania SMS-ów

## Użycie

```bash
# Dla pliku TXT (jeden numer w linii)
node scripts/import-sms-sent.js --file=sciezka/do/pliku.txt --format=txt

# Dla pliku JSON
node scripts/import-sms-sent.js --file=sciezka/do/pliku.json --format=json
```

## Format plików wejściowych

### Format TXT

Plik TXT powinien zawierać listę numerów telefonów, po jednym w każdej linii, np.:

```
123456789
987654321
555666777
```

### Format CSV

Plik CSV może zawierać kolumnę z numerami telefonów, np.:

```
"smsSendPhone"
"48690030536"
"48737707771"
"48539014771"
```

Podczas importu:
- Ignorowane są wartości tekstowe (np. nagłówki kolumn)
- Prefiks "48" jest automatycznie usuwany

### Format JSON

Plik JSON może mieć różne formaty:

1. Tablica stringów:

```json
[
  "123456789",
  "987654321",
  "555666777"
]
```

2. Tablica obiektów z polem `number` lub `phone`:

```json
[
  {"number": "123456789"},
  {"number": "987654321"},
  {"phone": "555666777"}
]
```

3. Obiekt z tablicą numerów:

```json
{
  "numbers": ["123456789", "987654321"],
  "phones": ["555666777"]
}
```

## Obsługa numerów telefonów

Skrypt automatycznie normalizuje numery telefonów:

- Usuwa wszystkie znaki nie będące cyframi (spacje, myślniki, nawiasy)
- Usuwa prefiks +48 lub 48 jeśli występuje
- Sprawdza czy po normalizacji numer ma 9 cyfr

## Przykładowe użycie

```bash
# Użycie z przykładowym plikiem TXT
node scripts/import-sms-sent.js --file=examples/phones-sample.txt --format=txt

# Użycie z przykładowym plikiem CSV
node scripts/import-sms-sent.js --file=examples/phones-sample.csv --format=csv

# Użycie z przykładowym plikiem JSON
node scripts/import-sms-sent.js --file=examples/phones-sample.json --format=json
```

## Debugowanie

Skrypt wyświetla szczegółowe informacje o wykonywanych operacjach, w tym:

- Liczbę zaimportowanych numerów
- Liczbę znalezionych ogłoszeń
- Liczbę zaktualizowanych ogłoszeń
- Liczbę usuniętych numerów z listy do wysłania

W przypadku błędów, skrypt wyświetla szczegółowe komunikaty o problemie.
